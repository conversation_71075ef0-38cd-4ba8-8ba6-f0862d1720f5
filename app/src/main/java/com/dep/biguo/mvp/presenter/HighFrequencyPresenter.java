package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.HighFrequencyBean;
import com.dep.biguo.mvp.contract.HighFrequencyContract;
import com.dep.biguo.mvp.ui.adapter.HighFrequencyAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.StartFinal;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.FragmentEvent;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class HighFrequencyPresenter extends BasePresenter<HighFrequencyContract.Model, HighFrequencyContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    @Inject HighFrequencyAdapter mHighFrequencyAdapter;

    @Inject
    public HighFrequencyPresenter(HighFrequencyContract.Model model, HighFrequencyContract.View rootView) {
        super(model, rootView);
    }

    public void getHighFrequency() {
        mModel.getHighFrequency(StartFinal.HIGH_FREQUENCY)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<HighFrequencyBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<HighFrequencyBean>> s) {
                        if (s.isSuccess()) {
                            if (AppUtil.isEmpty(s.getData())) {
                                mRootView.showEmptyView();
                            } else {
                                mHighFrequencyAdapter.setNewData(s.getData());
                                mRootView.hideLoading();
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        this.mHighFrequencyAdapter = null;
    }
}
