package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.GroupJoinBean;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.RealInfoBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.contract.GroupJoinTypeContract;
import com.dep.biguo.mvp.ui.adapter.GroupJoinAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class GroupJoinTypePresenter extends BasePresenter<GroupJoinTypeContract.Model, GroupJoinTypeContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject GroupJoinAdapter mGroupJoinAdapter;

    public static int limit = 20;//每一页请求数据的条数
    private String goodsType;
    private int orderId;//发起拼团后，返回的订单ID
    private int newcomers;//是否是新用户，0否，1是
    private boolean isResume;//activity是否处于onResume状态

    private boolean isGroup;

    private List<DiscountBean> discountList;//优惠券列表
    private PayResultListener mResultPayListener;//支付的监听接口

    private CountDownTimer mCountDownTimer;

    @Inject
    public GroupJoinTypePresenter(GroupJoinTypeContract.Model model, GroupJoinTypeContract.View rootView) {
        super(model, rootView);
    }

    public void init(Bundle bundle){
        goodsType = bundle.getString(StartFinal.GOODS_TYPE, "");
        newcomers = bundle.getInt(StartFinal.NEWCOMERS, 0);

        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess(isGroup);
                orderId = 0;
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {
                orderId = 0;
            }
        };

        //初始化倒计时
        mCountDownTimer = new CountDownTimer(86400 * 1000, 100) {
            @Override
            public void onTick(long millisUntilFinished) {
                computerCountDown();
            }

            @Override
            public void onFinish() {

            }
        };
    }

    public String getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }

    public int getOrderId() {
        return orderId;
    }

    public void getData(int page) {
        //0表示旧版的接口，不返回发起拼团的数据，1表示新版本的接口，需要返回发起拼团的数据
        int profession_id = UserCache.getProfession().getId();
        mModel.getData(profession_id, goodsType, page, limit, 1, newcomers)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<GroupJoinBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<GroupJoinBean> s) {
                        if (s.isSuccess()) {
                            if(s.getData() == null){
                                mRootView.showEmptyView();
                                mRootView.getDataFail();

                            }else {
                                List<GroupJoinBean.GroupBean> groupBeanList = new ArrayList<>(s.getData().getJoin_list());
                                groupBeanList.addAll(s.getData().getOpen_list());
                                if(AppUtil.isEmpty(groupBeanList)){
                                    mRootView.showEmptyView();
                                    mRootView.getDataFail();
                                    return;
                                }

                                mCountDownTimer.cancel();//先结束倒计时，再重新开始倒计时，避免出现多个倒计时
                                if(groupBeanList.size() > 0) {
                                    mCountDownTimer.start();//开始倒计时
                                }

                                for (int i = 0; i < groupBeanList.size(); i++) {
                                    GroupJoinBean.GroupBean groupBean = groupBeanList.get(i);
                                    //后台返回的单位是秒，需要转换成毫秒
                                    groupBean.setSec(groupBean.getSec() * 1000);
                                }

                                mRootView.showSuccessView();
                                mRootView.getDataSuccess(groupBeanList);
                            }

                        }else {
                            mRootView.showErrorView(null);
                            mRootView.getDataFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.getDataFail();
                    }
                });
    }

    /**获取优惠券
     *
     */
    public void getDiscountCard(boolean isGroup, int position, int newcomers){
        if(discountList != null) {
            mRootView.showPayDialog(isGroup, discountList, position, newcomers);
            return;
        }

        mModel.getDiscountCard(1, mGroupJoinAdapter.getItem(position).getType(), "use", -1)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<DiscountBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<List<DiscountBean>> s) {
                        if (s.isSuccess()) {
                            discountList = s.getData();
                            mRootView.showPayDialog(isGroup, discountList, position, newcomers);
                        }
                    }
                    @Override
                    public void onError(@NonNull Throwable t) {
                        mRootView.showMessage("获取优惠券列表失败");
                        mRootView.showPayDialog(isGroup, new ArrayList<>(), position, newcomers);
                    }
                });
    }

    /**查询是否已实名认证
     */
    public void getRealInfo(boolean isGroup, int position) {
        mModel.getRealInfo()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<RealInfoBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<RealInfoBean> response) {
                        if (response.isSuccess()) {
                            mRootView.realInfoSuccess(isGroup, response.getData(), position);
                        }
                    }
                });
    }

    public void pay(boolean isGroup, String groupId, String payType, GroupJoinBean.GroupBean groupBean, DiscountBean selectedDiscountBean) {
        //创建需要传递的参数对象
        Map<String,Object> paramsMap = getParams(groupId, payType, groupBean, selectedDiscountBean).getParamsMap();
        //根据是否拼团选择对应的接口
        Observable<BaseResponse<PayBean>> observable = isGroup ? mModel.payGroupOrder(paramsMap) : mModel.paySingleOrder(paramsMap);

        observable.subscribeOn(Schedulers.io())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            GroupJoinTypePresenter.this.isGroup = isGroup;
                            orderId = s.getData().getOrder_id();
                            //当调起的订单使用了优惠券，优惠券此时被占用，则需要将使用的优惠券从优惠券列表中移除
                            if(selectedDiscountBean != null) {
                                discountList.remove(selectedDiscountBean);
                            }
                            //优惠额度大于等于支付价格，不需要调起支付界面，处理方式与果币、积分支付一致，
                            if(s.getData() != null && s.getData().getPay() instanceof Boolean){
                                mResultPayListener.onPaySuccess();
                                return;
                            }

                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                WXPayBean wxPayBean = GsonUtils.fromJson(GsonUtils.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_COIN.equals(payType) || PayUtils.PAY_TYPE_INTEGRAL.equals(payType)){
                                mResultPayListener.onPaySuccess();
                            }
                        }
                    }
                });
    }

    /**创建一个创建订单需要的参数集合
     *
     */
    private PayParamsBean getParams(String groupId, String payType, GroupJoinBean.GroupBean groupBean, DiscountBean selectedDiscountBean) {
        PayParamsBean paramsBean = PayParamsBean.init();
        paramsBean.put(PayParamsBean.CODE,groupBean.getCode());
        paramsBean.put(PayParamsBean.TYPE, goodsType);
        paramsBean.put(PayParamsBean.GROUP_ID, groupId);
        paramsBean.put(PayParamsBean.PAY_TYPE, payType);
        paramsBean.put(PayParamsBean.COUPON_ID, selectedDiscountBean == null ? 0 : selectedDiscountBean.getId());
        paramsBean.put(PayParamsBean.NEWCOMERS, groupBean.getIs_newcomers());
        paramsBean.put(PayParamsBean.IS_REPLENISH, "1");//默认传1，表示都需要升级

        //视频需要传递product_id
        if(StartFinal.VIDEO1.equals(goodsType) || StartFinal.VIDEO2.equals(goodsType) || StartFinal.VIDEO3.equals(goodsType) || StartFinal.VIDEO4.equals(goodsType)){
            paramsBean.put(PayParamsBean.PRODUCT_ID, groupBean.getProduct_id());
            paramsBean.put(PayParamsBean.SOURCE_TYPE, groupBean.getSource_type());
        }



        return paramsBean;
    }

    public String getGoodTypeName(String goodsType, int source_type){
        if(StartFinal.VIP.equals(goodsType)){
            return "VIP题库";
        }else if(StartFinal.YAMI.equals(goodsType)){
            return "考前押密";
        }else if(StartFinal.HIGH_FREQUENCY.equals(goodsType)){
            return "高频考点";
        }else if(StartFinal.VIDEO2.equals(goodsType)){
            return "串讲视频";
        }else if(StartFinal.VIDEO3.equals(goodsType) && source_type == 0){
            return "直播密训班";
        }else if(StartFinal.VIDEO4.equals(goodsType)){
            return "直播特训班";
        }else {
            return "精讲视频";
        }
    }

    public void onResume(){
        //倒计时功能可以刷新UI
        isResume = true;
    }

    public void onPause(){
        //倒计时功能不需要刷新UI
        isResume = false;
    }

    /**计算倒计时
     *
     */
    private void computerCountDown() {
        if (AppUtil.isEmpty(mGroupJoinAdapter.getData())) return;

        for (GroupJoinBean.GroupBean bean : mGroupJoinAdapter.getData()) {
            bean.setSec(bean.getSec() <= 0 ? 0 : (bean.getSec() - 100));
        }

        if(isResume && mGroupJoinAdapter != null) {
            mGroupJoinAdapter.notifyTime();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        if(mCountDownTimer != null) mCountDownTimer.cancel();
        LoadingDialog.hideLoadingDialog();
    }
}
