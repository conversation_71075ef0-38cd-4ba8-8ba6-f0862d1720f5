package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.GroupCommentBean;
import com.dep.biguo.mvp.contract.ProductDetailStandaloneContract;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class ProductDetailStandaloneFragPresenter extends BasePresenter<ProductDetailStandaloneContract.Model, ProductDetailStandaloneContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public ProductDetailStandaloneFragPresenter(ProductDetailStandaloneContract.Model model, ProductDetailStandaloneContract.View rootView) {
        super(model, rootView);
    }

    public void loadComments(int page, int limit){
        int goodsId = mRootView.getGoodsId();
        mModel.getComments(goodsId, page, limit)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<GroupCommentBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<GroupCommentBean>> s) {
                        if (s.isSuccess()) {
                            List<GroupCommentBean> list = s.getData();
                            mRootView.showComments(list);
                            int count = mRootView.getInitialCommentCount();
                            if (count <= 0) count = list == null ? 0 : list.size();
                            mRootView.setCommentCountTitle(count);
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }
}

