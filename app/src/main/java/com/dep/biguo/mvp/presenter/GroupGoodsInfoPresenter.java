package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.os.CountDownTimer;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.GroupBean;
import com.dep.biguo.bean.GroupGoodInfoBean;
import com.dep.biguo.bean.RuleBean;
import com.dep.biguo.mvp.contract.GroupGoodsInfoContract;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class GroupGoodsInfoPresenter extends BasePresenter<GroupGoodsInfoContract.Model, GroupGoodsInfoContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private CountDownTimer mCountDownTimer;
    private GroupGoodInfoBean infoBean;

    @Inject
    public GroupGoodsInfoPresenter(GroupGoodsInfoContract.Model model, GroupGoodsInfoContract.View rootView) {
        super(model, rootView);
    }

    public void init(GroupGoodInfoBean infoBean){
        this.infoBean = infoBean;
        //初始化倒计时
        mCountDownTimer = new CountDownTimer(86400 * 1000, 100) {
            @Override
            public void onTick(long millisUntilFinished) {
                if(mRootView != null) {
                    computerCountDown();
                }
            }

            @Override
            public void onFinish() {

            }
        };
    }

    public void countDownStart(){
        mCountDownTimer.start();
    }

    public void countDownCancel(){
        mCountDownTimer.cancel();
    }

    /**计算倒计时
     *
     */
    private void computerCountDown() {
        if (AppUtil.isEmpty(infoBean.getConduct_group())) return;

        for (GroupBean bean : infoBean.getConduct_group()) {
            bean.setSec(bean.getSec() <= 0 ? 0 : (bean.getSec() - 100));
        }

        mRootView.refreshJoinDialog();
    }

    public void getRule(String type, String title) {
        mModel.getRule(type)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<RuleBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<RuleBean> response) {
                        if (response.isSuccess()) {
                            mRootView.getRuleSuccess(title, response.getData().getRules());
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        if(mCountDownTimer != null) mCountDownTimer.cancel();
        LoadingDialog.hideLoadingDialog();
    }
}
