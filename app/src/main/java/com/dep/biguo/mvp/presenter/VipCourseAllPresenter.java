package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.VipCourseBean;
import com.dep.biguo.mvp.contract.VipCourseAllContract;
import com.dep.biguo.mvp.ui.adapter.VipCourseAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.FragmentEvent;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;


/**
 * ================================================
 * Description:
 * <p>
 * Created by MVPArmsTemplate on 06/22/2020 09:38
 * ================================================
 */
@FragmentScope
public class VipCourseAllPresenter extends BasePresenter<VipCourseAllContract.Model, VipCourseAllContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;
    @Inject
    VipCourseAdapter mVipCourseAdapter;

    @Inject
    public VipCourseAllPresenter(VipCourseAllContract.Model model, VipCourseAllContract.View rootView) {
        super(model, rootView);
    }

    public void getVipCourse(boolean showLoading,boolean isUpdate) {
        int ProfessionsId = 0;
        int ProvinceId = 0;
        if (UserCache.getUserCache() != null) {
            if (UserCache.getUserCache().getProfessions_id() > 0 && UserCache.getUserCache().getProvince_id() > 0) {
                ProfessionsId = UserCache.getUserCache().getProfessions_id();
                ProvinceId = UserCache.getUserCache().getProvince_id();
            }

        } else {
            ProfessionsId = UserCache.getProfession().getId();
            ProvinceId = UserCache.getProvince().getId();
        }
        mModel.getVipCourse(ProfessionsId, ProvinceId,isUpdate)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if (showLoading)
                        mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<VipCourseBean>(mErrorHandler) {
                    @Override
                    public void onNext(VipCourseBean s) {
                        if (showLoading)
                            mRootView.hideLoading();
                        if (s.isSuccess()) {
                            if (AppUtil.isEmpty(s.getData())) {
                                mRootView.showEmptyView();
                            } else {

                                mVipCourseAdapter.setNewData(s.getData());
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        this.mVipCourseAdapter = null;
    }
}
