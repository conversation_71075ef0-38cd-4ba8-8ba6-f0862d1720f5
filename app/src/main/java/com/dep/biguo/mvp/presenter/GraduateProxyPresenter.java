package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CounsellingDazikaoBean;
import com.dep.biguo.bean.GraduateProxyBean;
import com.dep.biguo.mvp.contract.GraduateProxyContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class GraduateProxyPresenter extends BasePresenter<GraduateProxyContract.Model, GraduateProxyContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public GraduateProxyPresenter(GraduateProxyContract.Model model, GraduateProxyContract.View rootView) {
        super(model, rootView);
    }
    /**获取介绍详情
     */
    public void getAssistiveClassDetail() {
        mModel.getGraduateProxyDetail()
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<GraduateProxyBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<GraduateProxyBean> response) {
                        if(response.isSuccess()){
                            if(response.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getGraduateProxyDetailSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
