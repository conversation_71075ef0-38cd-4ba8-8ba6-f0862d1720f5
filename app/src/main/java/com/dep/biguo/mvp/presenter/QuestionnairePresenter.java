package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;
import android.os.Build;
import androidx.annotation.RequiresApi;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.SurveyQuestionBean;
import com.dep.biguo.mvp.contract.QuestionnaireContract;
import com.dep.biguo.mvp.ui.adapter.SurveyAdapter;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;


/**
 * ================================================
 * Description:
 * <p>调查问卷
 * Created by MVPArmsTemplate on 06/05/2020 16:43
 * ================================================
 */
@ActivityScope
public class QuestionnairePresenter extends BasePresenter<QuestionnaireContract.Model, QuestionnaireContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;
    @Inject
    SurveyAdapter mSurveyAdapter;
    private List<SurveyQuestionBean> surveyQuestionBeans = new ArrayList<>();
    private HashSet<String> optionBeans = new HashSet<>();

    private int index = -1;
    private boolean isLastQuestion = false;

    @Inject
    public QuestionnairePresenter(QuestionnaireContract.Model model, QuestionnaireContract.View rootView) {
        super(model, rootView);
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    void onCreate() {
        getLabelList();
    }

    public void getLabelList() {
        /*mModel.getLabelList()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<SurveyQuestionBean>>(mErrorHandler) {
                    @RequiresApi(api = Build.VERSION_CODES.N)
                    @Override
                    public void onNext(BaseResponse<SurveyQuestionBean> response) {
                        if (response.isSuccess()) {
                            *//*surveyQuestionBeans = response.getData();
                            Collections.sort(surveyQuestionBeans, (o1, o2) -> o1.getType()-o2.getType());
                            //surveyQuestionBeans.sort((bean, bean2) -> bean.getType() - bean2.getType());
                            mSurveyAdapter.setNewData(surveyQuestionBeans.get(0).getList());*//*
                            index = 0;
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });*/
    }

    public void onSurveyOptionBeans(int pos) {
        switch (index) {
            case 0:
                UserCache.cacheLabel(mSurveyAdapter.getData().get(pos).getTitle());
                break;
            case 1:
                UserCache.cacheEducation(mSurveyAdapter.getData().get(pos).getTitle());
                break;
            case 2:
                UserCache.cacheExamReference(mSurveyAdapter.getData().get(pos).getTitle());
                break;
            default:
                break;
        }
        index++;
        if (index < surveyQuestionBeans.size()) {
            boolean b = index == surveyQuestionBeans.size() - 1;
            setLastQuestion(b);
            mRootView.onUpdateOption(surveyQuestionBeans.get(index), b);
        }
    }

    public void onReasonOptions(int pos, boolean isAdd) {
        if (isAdd) {
            optionBeans.add(mSurveyAdapter.getItem(pos).getTitle());
        } else {
            optionBeans.remove(mSurveyAdapter.getItem(pos).getTitle());
        }
    }

    public boolean submitOptions() {
        if (optionBeans.size() > 0) {
            UserCache.cacheReason(optionBeans);
            return true;
        } else {
            return false;
        }
    }

    public boolean isLastQuestion() {
        return isLastQuestion;
    }

    public void setLastQuestion(boolean lastQuestion) {
        isLastQuestion = lastQuestion;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        surveyQuestionBeans.clear();
        optionBeans.clear();
        surveyQuestionBeans = null;
        optionBeans = null;
        index = -1;
    }
}
