package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.RealInfoBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.mvp.contract.UserInfoEditContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.RSAEncrypt;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class UserInfoEditPresenter extends BasePresenter<UserInfoEditContract.Model, UserInfoEditContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public UserInfoEditPresenter(UserInfoEditContract.Model model, UserInfoEditContract.View rootView) {
        super(model, rootView);
    }
    public void updateUserInfo(String nickname, String email, String realName, String IDCard) {
        if(TextUtils.isEmpty(nickname)){
            mRootView.showMessage("请输入昵称");
            return;
        }else if(nickname.contains("\n")){
            mRootView.showMessage("昵称中不要包含换行");
            return;
        }else if(email.contains("\n")){
            mRootView.showMessage("邮箱中不要包含换行");
            return;
        }

        if(!TextUtils.isEmpty(realName + IDCard)){
            if(TextUtils.isEmpty(realName) || TextUtils.isEmpty(IDCard)){
                mRootView.showMessage("请完善实名信息");
                return;
            }else if(realName.contains("\n")){
                mRootView.showMessage("真实姓名中不要包含换行");
                return;
            }else if(IDCard.contains("\n")){
                mRootView.showMessage("身份证号中不要包含换行");
                return;
            }
        }

        mModel.updateUserInfo(nickname, email, realName, IDCard)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.showMessage("修改成功");
                            UserBean bean = UserCache.getUserCache();
                            bean.setNickname(nickname);
                            if(TextUtils.isEmpty(bean.getRealname())) bean.setRealname(realName);
                            if(TextUtils.isEmpty(bean.get_idcard())) bean.set_idcard(IDCard);
                            if(TextUtils.isEmpty(bean.getEmail())) bean.setEmail(email);
                            UserCache.cacheUser(bean);
                            mRootView.updateUserInfoSuccess();
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
