package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.os.Bundle;
import android.os.CountDownTimer;

import androidx.annotation.NonNull;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.MyGroupBean;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.SkillMyGroupContract;
import com.dep.biguo.mvp.ui.adapter.MyGroupAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class SkillMyGroupPresenter extends BasePresenter<SkillMyGroupContract.Model, SkillMyGroupContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;


    @Inject
    MyGroupAdapter mMyGroupAdapter;

    private int mPage = 1;
    private static final int INIT = 0;
    private static final int REFRESH = 1;
    private static final int LOAD = 2;

    private String goodsType;
    private List<DiscountBean> discountList;//优惠券列表
    private PayResultListener mResultPayListener;//支付的监听接口

    private String payGroupId;//发起拼团的ID
    private String payGoodsType;//发起拼团的商品类型
    private int skill_id;//1是新人，0不是新人

    private CountDownTimer mCountDownTimer;

    @Inject
    public SkillMyGroupPresenter(SkillMyGroupContract.Model model, SkillMyGroupContract.View rootView) {
        super(model, rootView);
    }

    public int getSkill_id() {
        return skill_id;
    }

    public void init(Bundle bundle){
        goodsType = bundle.getString(StartFinal.GOODS_TYPE);
        skill_id = bundle.getInt(StartFinal.SKILL_ID, 1);
        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess(payGroupId, payGoodsType, skill_id);
                payGroupId = null;
                payGoodsType = null;
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {
                mRootView.payCancel();
                payGroupId = null;
                payGoodsType = null;
            }
        };
        //初始化倒计时
        if(mCountDownTimer == null) {
            mCountDownTimer = new CountDownTimer(86400 * 1000, 100) {
                @Override
                public void onTick(long millisUntilFinished) {
                    computerCountDown();
                }

                @Override
                public void onFinish() {

                }
            };
        }

        initRefresh();
    }

    public void initRefresh(){
        getData(INIT,1);
    }

    public void refresh(){
        getData(REFRESH,1);
    }

    public void loadMore(){
        getData(LOAD,mPage+1);
    }

    private void getData(int action, int page) {
        mModel.getData(goodsType, skill_id, page)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> { if(action == INIT) mRootView.showLoading(); })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<MyGroupBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<MyGroupBean>> s) {
                        if (s.isSuccess()) {
                            mCountDownTimer.cancel();//先结束倒计时，再重新开始倒计时，避免出现多个倒计时
                            if (action == INIT || action == REFRESH) {
                                mMyGroupAdapter.setNewData(s.getData());
                                mRootView.finishRefresh();
                                if (AppUtil.isEmpty(s.getData())) {
                                    mRootView.showEmptyView();
                                }else {
                                    mRootView.hideLoading();
                                }
                                mPage = 1;//刷新成功，将页数设置为1
                            } else {
                                mMyGroupAdapter.addData(s.getData());
                                mMyGroupAdapter.loadMoreComplete();
                                mPage += 1;//加载更多成功，将请求页数自加1
                            }

                            if(mMyGroupAdapter.getItemCount() > 0){
                                mCountDownTimer.start();//开始倒计时
                            }

                            if(!AppUtil.isEmpty(s.getData())) {
                                for (int i = 0; i < s.getData().size(); i++) {
                                    MyGroupBean groupBean = s.getData().get(i);
                                    groupBean.setSec(groupBean.getSec() * 1000);
                                }
                            }else {
                                mMyGroupAdapter.loadMoreEnd();
                            }
                        }else {
                            refreshOrLoadAnimation();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        refreshOrLoadAnimation();
                        if (AppUtil.isEmpty(mMyGroupAdapter.getData()))
                            mRootView.showErrorView(t);
                    }

                    //取消刷新或加载的动画
                    public void refreshOrLoadAnimation(){
                        switch (action){
                            case REFRESH:mRootView.finishRefresh();break;
                            case LOAD:mMyGroupAdapter.loadMoreFail();break;
                        }
                    }
                });
    }
    /**获取优惠券
     *
     */
    public void getDiscountCard(MyGroupBean payGroupBean){
        if(discountList != null) {
            mRootView.showPayDialog(discountList, payGroupBean);
            return;
        }

        mModel.getDiscountCard(1, payGroupBean.getType(), "use", -1)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<DiscountBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<List<DiscountBean>> s) {
                        if (s.isSuccess()) {
                            discountList = s.getData();
                            mRootView.showPayDialog(discountList, payGroupBean);
                        }
                    }
                    @Override
                    public void onError(@NonNull Throwable t) {
                        mRootView.showMessage("获取优惠券列表失败");
                        mRootView.showPayDialog(new ArrayList<>(), payGroupBean);
                    }
                });
    }

    public void pay(String courseCode, String payType, MyGroupBean goodsBean, DiscountBean selectedDiscountBean) {
        LogUtil.d("dddd", goodsBean);
        //创建需要传递的参数对象
        Map<String,Object> paramsMap = getParams(courseCode, payType, goodsBean, selectedDiscountBean).getParamsMap();
        //根据是否拼团选择对应的接口
        mModel.payGroupOrder(paramsMap)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            //记录正在支付商品，支付成功或取消，都将置为null
                            payGroupId = s.getData().getOrder_id()+"";
                            payGoodsType = goodsBean.getType();
                            //当调起的订单使用了优惠券，优惠券此时被占用，则需要将使用的优惠券从优惠券列表中移除
                            if(selectedDiscountBean != null) {
                                discountList.remove(selectedDiscountBean);
                            }
                            //优惠额度大于等于支付价格，不需要调起支付界面，处理方式与果币、积分支付一致，
                            if(s.getData() != null && s.getData().getPay() instanceof Boolean){
                                mResultPayListener.onPaySuccess();
                                payGroupId = null;
                                payGoodsType = null;
                                return;
                            }

                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                WXPayBean wxPayBean = GsonUtils.fromJson(GsonUtils.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }
                        }
                    }
                });
    }


    public String getGoodTypeName(String goodsType){
        return StartFinal.SKILL_VIDEO.equals(goodsType) ? "职业技能" : "职场提升";
    }


    /**创建一个创建订单需要的参数集合
     * @param courseCode 课程代码，支持多选，以"_"连接
     * @param payType 支付方式{@link PayUtils#PAY_TYPE_WEXIN,PayUtils#PAY_TYPE_ALIPAY,PayUtils#PAY_TYPE_INTEGRAL,PayUtils#PAY_TYPE_COIN}
     * @return
     */
    private PayParamsBean getParams(String courseCode, String payType, MyGroupBean payGoodsBean, DiscountBean selectedDiscountBean) {
        PayParamsBean paramsBean = PayParamsBean.init();
        paramsBean.put(PayParamsBean.CODE,courseCode);
        paramsBean.put(PayParamsBean.TYPE, payGoodsBean.getType());
        paramsBean.put(PayParamsBean.R_ID, "0");
        paramsBean.put(PayParamsBean.IS_GROUP, "1");//1拼团，0单独购买
        paramsBean.put(PayParamsBean.PAY_TYPE, payType);
        paramsBean.put(PayParamsBean.COUPON_ID, selectedDiscountBean == null ? 0 : selectedDiscountBean.getId());
        paramsBean.put(PayParamsBean.PRODUCT_ID, payGoodsBean.getProduct_id());
        paramsBean.put(PayParamsBean.SOURCE_TYPE, payGoodsBean.getSource_type());
        paramsBean.put(PayParamsBean.SKILL_ID, payGoodsBean.getSkill_id());
        return paramsBean;
    }

    /**计算倒计时
     *
     */
    private void computerCountDown() {
        if (AppUtil.isEmpty(mMyGroupAdapter.getData())) return;

        for (MyGroupBean bean : mMyGroupAdapter.getData()) {
            bean.setSec(bean.getSec() <= 0 ? 0 : (bean.getSec() - 100));
        }

        mMyGroupAdapter.notifyTime();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        if(mCountDownTimer != null) mCountDownTimer.cancel();
        LoadingDialog.hideLoadingDialog();
    }
}
