package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CityBean;
import com.dep.biguo.bean.MakeStudyPlanBean;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.bean.ProfessionInfo;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.SchoolBean;
import com.dep.biguo.mvp.contract.MakeStudyPlanContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.simple.eventbus.EventBus;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class MakeStudyPlanPresenter extends BasePresenter<MakeStudyPlanContract.Model, MakeStudyPlanContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public MakeStudyPlanPresenter(MakeStudyPlanContract.Model model, MakeStudyPlanContract.View rootView) {
        super(model, rootView);
    }

    public void getMakeStudyPlan(int plan_id) {
        Observable<BaseResponse<MakeStudyPlanBean>> observable;
        if(plan_id == 0) {
            observable = mModel.getStudyPlan();
        }else {
            observable = mModel.getExistStudyPlan(plan_id);
        }
        observable.subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<MakeStudyPlanBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<MakeStudyPlanBean> response) {
                        if (response.isSuccess()) {
                            mRootView.showSuccessView();
                            mRootView.getStudyPlanSuccess(response.getData());
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void submitMakeStudyPlan(int plan_id, int plan_day, int is_shroud, List<String> add_course) {
        if(AppUtil.isEmpty(add_course)){
            mRootView.showMessage("请选择至少一门科目");
            return;
        }

        StringBuilder addBuilder = new StringBuilder();
        for(String code : add_course){
            addBuilder.append(code).append(",");
        }
        if(addBuilder.length() > 0){
            addBuilder.delete(addBuilder.length()-1, addBuilder.length());
        }

        Observable<BaseResponse> observable;
        if(plan_id == 0) {
            observable = mModel.submitStudyPlan(plan_day, is_shroud, addBuilder.toString());
        }else {
            observable = mModel.updateStudyPlan(plan_id, plan_day, addBuilder.toString());
        }
        observable.subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.submitStudyPlanSuccess();
                        }
                    }
                });
    }


    public void cancelMakeStudyPlan(int plan_id) {
        mModel.cancelMakeStudyPlan(plan_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.cancelStudyPlanSuccess();
                        }
                    }
                });
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
