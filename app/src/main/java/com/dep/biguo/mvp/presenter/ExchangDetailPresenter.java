package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.ExchangeDetailBean;
import com.dep.biguo.mvp.contract.ExchangDetailContract;
import com.dep.biguo.mvp.ui.adapter.ExchangeDetailAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;


@ActivityScope
public class ExchangDetailPresenter extends BasePresenter<ExchangDetailContract.Model, ExchangDetailContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject ExchangeDetailAdapter mAdapter;

    @Inject
    public ExchangDetailPresenter(ExchangDetailContract.Model model, ExchangDetailContract.View rootView) {
        super(model, rootView);
    }

    public void cashListData(int page) {
        mModel.cashList(UserCache.getUserCache().getUser_id(), page)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<ExchangeDetailBean.ExchangeBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<ExchangeDetailBean.ExchangeBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showEmptyView();
                                mRootView.cashListSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.cashListSuccess(response.getData());
                            }

                        } else {
                            mRootView.showErrorView(null);
                            mRootView.cashListFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.cashListFail();
                    }
                });
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }
}
