package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.MyDeductionCardBean;
import com.dep.biguo.mvp.contract.MyDeductionCardContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class MyDeductionCardPresenter extends BasePresenter<MyDeductionCardContract.Model, MyDeductionCardContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public MyDeductionCardPresenter(MyDeductionCardContract.Model model, MyDeductionCardContract.View rootView) {
        super(model, rootView);
    }

    public void getMyDeductionCard(int page){
        mModel.getMyDeductionCard(page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<MyDeductionCardBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<MyDeductionCardBean>> s) {
                        if (s.isSuccess()) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.showEmptyView();
                                mRootView.getMyDeductionCardSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getMyDeductionCardSuccess(s.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                            mRootView.getMyDeductionCardFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.getMyDeductionCardFail();
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
