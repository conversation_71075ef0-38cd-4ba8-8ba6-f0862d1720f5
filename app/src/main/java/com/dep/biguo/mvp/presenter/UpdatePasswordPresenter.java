package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.VerifyCodeBean;
import com.dep.biguo.mvp.contract.UpdatePasswordContract;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.RSAEncrypt;
import com.biguo.utils.widget.LoadingDialog;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

@ActivityScope
public class UpdatePasswordPresenter extends BasePresenter<UpdatePasswordContract.Model, UpdatePasswordContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    @Inject
    public UpdatePasswordPresenter(UpdatePasswordContract.Model model, UpdatePasswordContract.View rootView) {
        super(model, rootView);
    }

    public void resetPwdByOriginal(String mobile, String originalPwd, String newPwd) {

        if (TextUtils.isEmpty(mobile)) {
            mRootView.showMessage("请输入您的手机号");
            return;
        }

        if (TextUtils.isEmpty(originalPwd)) {
            mRootView.showMessage("请输入您的旧密码");
            return;
        }

        if (TextUtils.isEmpty(newPwd)) {
            mRootView.showMessage("请输入您的新密码");
            return;
        }

        if (newPwd.length() < 6) {
            mRootView.showMessage("请输入6位数以上的密码");
            return;
        }

        mModel.resetPwdByOriginal(mobile, originalPwd, newPwd)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.showMessage(response.getResult_info());
                            mRootView.killMyself();
                        }
                    }
                });
    }

    public void getSmsVerifyCode(String mobile, String captcha) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("mobile", mobile);
            params.put("captcha", captcha);
            LogUtil.d("dddd",new Gson().toJson(params));
            mModel.verify_code_by_image_new(RSAEncrypt.encryptByPublicKey(new Gson().toJson(params)))
                    .subscribeOn(Schedulers.io())
                    
                    .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                    .subscribeOn(AndroidSchedulers.mainThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .doFinally(() -> mRootView.hideLoadingDialog())
                    .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                    .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                        @Override
                        public void onNext(BaseResponse userBeanBaseResponse) {
                            if (userBeanBaseResponse.isSuccess()) {
                                mRootView.sendVerifyCodeSuccess();
                                mRootView.showMessage(userBeanBaseResponse.getResult_info());
                            }
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void resetPwdByVerify(String mobile, String newPwd, String veridyCode) {

        if (TextUtils.isEmpty(mobile)) {
            mRootView.showMessage("请输入您的手机号");
            return;
        }

        if (TextUtils.isEmpty(veridyCode)) {
            mRootView.showMessage("请输入验证码");
            return;
        }

        if (TextUtils.isEmpty(newPwd)) {
            mRootView.showMessage("请输入您的新密码");
            return;
        }

        if (newPwd.length() < 6) {
            mRootView.showMessage("请输入6位数以上的密码");
            return;
        }

        mModel.resetPwdByVerify(mobile, newPwd, veridyCode)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.showMessage(response.getResult_info());
                            mRootView.killMyself();
                        }
                    }
                });
    }

    public void getVerifyImage(String mobile) {
        mModel.getVerifyImage(mobile)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<VerifyCodeBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<VerifyCodeBean> response) {
                        if (response.isSuccess()) {
                            mRootView.getVerifyImageSuccess(response.getData());
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
