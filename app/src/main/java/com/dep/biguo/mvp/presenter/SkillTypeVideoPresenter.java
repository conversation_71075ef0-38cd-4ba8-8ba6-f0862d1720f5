package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.SkillTypeVideoBean;
import com.dep.biguo.mvp.contract.SkillTypeVideoContract;
import com.dep.biguo.mvp.ui.adapter.SkillTypeVideoAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.FragmentEvent;

import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class SkillTypeVideoPresenter extends BasePresenter<SkillTypeVideoContract.Model, SkillTypeVideoContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject SkillTypeVideoAdapter skillTypeVideoAdapter;

    private int mPage = 1;
    private static final int INIT = 0;
    private static final int REFRESH = 1;
    private static final int LOAD = 2;

    @Inject
    public SkillTypeVideoPresenter(SkillTypeVideoContract.Model model, SkillTypeVideoContract.View rootView) {
        super(model, rootView);
    }

    public void initRefresh(int skill_id, int source_type){
        getData(skill_id, source_type, INIT,1);
    }

    public void refresh(int skill_id, int source_type){
        getData(skill_id, source_type, REFRESH,1);
    }

    public void loadMore(int skill_id, int source_type){
        getData(skill_id, source_type, LOAD,mPage+1);
    }

    private void getData(int skill_id, int source_type, int action, int page) {
        mModel.getTypeVideo(skill_id, source_type, page)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if (action == INIT)
                        mRootView.showRefresh();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> {
                    if (action == INIT)
                        mRootView.finishRefresh();
                })
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.DESTROY_VIEW))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<SkillTypeVideoBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<SkillTypeVideoBean>> s) {
                        if (s.isSuccess()) {
                            if (action == INIT || action == REFRESH) {
                                skillTypeVideoAdapter.setNewData(s.getData());
                                mRootView.finishRefresh();
                                if (AppUtil.isEmpty(s.getData()))
                                    mRootView.showEmptyView();
                                mPage = 1;//刷新成功，将页数设置为1
                            } else {
                                skillTypeVideoAdapter.addData(s.getData());
                                skillTypeVideoAdapter.loadMoreComplete();
                                mPage += 1;//加载更多成功，将请求页数自加1
                            }

                            if (AppUtil.isEmpty(s.getData()))
                                skillTypeVideoAdapter.loadMoreEnd();
                        }else {
                            refreshOrLoadAnimation();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        refreshOrLoadAnimation();
                        if (AppUtil.isEmpty(skillTypeVideoAdapter.getData()))
                            mRootView.showErrorView(t);
                    }

                    //取消刷新或加载的动画
                    public void refreshOrLoadAnimation(){
                        switch (action){
                            case REFRESH:mRootView.finishRefresh();break;
                            case LOAD:skillTypeVideoAdapter.loadMoreFail();break;
                        }
                    }
                });
    }

    /**获取视频的播放地址
     *
     */
    public void getVideoPlayUrl(SkillTypeVideoBean videoBean){
        mModel.getVideoPlayUrl(videoBean.getSource_type(), videoBean.getProduct_id(), videoBean.getCourse_id(), videoBean.getChapter_id(), videoBean.getItem_id(), videoBean.getCst_id(), videoBean.getIs_free())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            String json = GsonUtils.toJson(response.getData());
                            Map<String, String> map = GsonUtils.fromJson(json, new TypeToken<Map<String, String>>(){}.getType());
                            String url = map.get("video_url");
                            if(TextUtils.isEmpty(url)){
                                mRootView.showMessage("未获取到播放链接");
                                return;
                            }
                            mRootView.getVideoUrlSuccess(videoBean, url);
                        }
                    }

                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
