package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.GroupCommentBean;
import com.dep.biguo.mvp.contract.ProductDetailStandaloneContract;
import com.dep.biguo.bean.BaseResponse;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class ProductDetailStandalonePresenter extends BasePresenter<ProductDetailStandaloneContract.Model, ProductDetailStandaloneContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public ProductDetailStandalonePresenter(ProductDetailStandaloneContract.Model model, ProductDetailStandaloneContract.View rootView) {
        super(model, rootView);
    }

    public void loadComments(int page, int limit){
        int goodsId = mRootView.getGoodsId();
        mModel.getComments(goodsId, page, limit)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<GroupCommentBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<GroupCommentBean>> s) {
                        if (s.isSuccess()) {
                            List<GroupCommentBean> list = s.getData();
                            mRootView.showComments(list);
                            // If initial count is provided by caller, use it to set title; else use list size
                            int count = mRootView.getInitialCommentCount();
                            if (count <= 0) count = list == null ? 0 : list.size();
                            mRootView.setCommentCountTitle(count);
                        }
                    }
                });
    }

    public void addShopCart(int goodsId){
        android.util.Log.d("AddToCart", "========== ProductDetailStandalonePresenter.addShopCart START ===========");
        android.util.Log.d("AddToCart", "Goods ID: " + goodsId);
        android.util.Log.d("AddToCart", "Timestamp: " + System.currentTimeMillis());
        
        mModel.addShopCart(1, goodsId)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    android.util.Log.d("AddToCart", "Network request started");
                })
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        android.util.Log.d("AddToCart", "Response received:");
                        android.util.Log.d("AddToCart", "  - Success: " + s.isSuccess());
                        android.util.Log.d("AddToCart", "  - Code: " + s.getResult_code());
                        android.util.Log.d("AddToCart", "  - Message: " + s.getResult_info());
                        
                        if (s.isSuccess()) {
                            android.util.Log.d("AddToCart", "Add to cart successful! Showing message");
                            mRootView.showMessage(s.getResult_info());
                        } else {
                            android.util.Log.d("AddToCart", "Add to cart failed! Server returned failure");
                        }
                        android.util.Log.d("AddToCart", "========== ProductDetailStandalonePresenter.addShopCart END ===========");
                    }
                    
                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        android.util.Log.e("AddToCart", "Error occurred: " + t.getMessage(), t);
                        android.util.Log.d("AddToCart", "========== ProductDetailStandalonePresenter.addShopCart ERROR END ===========");
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }
}

