package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.IncomeBean;
import com.dep.biguo.bean.InviteStatisticsBean;
import com.dep.biguo.mvp.contract.InviteIncomeContract;
import com.dep.biguo.mvp.ui.adapter.IncomeAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.FragmentEvent;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class InviteIncomePresenter extends BasePresenter<InviteIncomeContract.Model, InviteIncomeContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private Map<String, InviteStatisticsBean>  statisticsMap;
    private List<IncomeBean> list;

    public Map<String, InviteStatisticsBean> getStatisticsMap() {
        return statisticsMap;
    }

    @Inject
    public InviteIncomePresenter(InviteIncomeContract.Model model, InviteIncomeContract.View rootView) {
        super(model, rootView);
    }

    public void getIncomeList(int page) {
        mModel.getIncomeList(page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.DESTROY_VIEW))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<IncomeBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<IncomeBean>> s) {
                        if (s.isSuccess() && statisticsMap != null) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.showEmptyView();
                                mRootView.getIncomeListSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getIncomeListSuccess(s.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }
    public void getInviteStatistics(int page) {
        mModel.getInviteStatistics()
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.DESTROY_VIEW))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<Map<String, InviteStatisticsBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<Map<String, InviteStatisticsBean>> s) {
                        if (s.isSuccess()) {
                            statisticsMap = s.getData();
                            getIncomeList(page);
                        }
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
