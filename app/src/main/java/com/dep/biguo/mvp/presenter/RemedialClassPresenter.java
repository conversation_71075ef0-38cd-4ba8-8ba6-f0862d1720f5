package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.RemediaClassBean;
import com.dep.biguo.bean.VideoLiveBean;
import com.dep.biguo.live.LiveHelper;
import com.dep.biguo.mvp.contract.RemedialClassContract;
import com.dep.biguo.mvp.ui.adapter.HomeBookAdapter;
import com.dep.biguo.mvp.ui.adapter.HomeClassAdapter;
import com.dep.biguo.mvp.ui.adapter.VideoCourseAdapter;
import com.dep.biguo.mvp.ui.adapter.VideoLiveAdapter;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.FragmentEvent;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;


/**
 * ================================================
 * Description:
 * <p>
 * ================================================
 */
@FragmentScope
public class RemedialClassPresenter extends BasePresenter<RemedialClassContract.Model, RemedialClassContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    @Inject
    HomeClassAdapter mClassAdapter;//VIP课堂
    @Inject
    HomeBookAdapter mBookAdapter;//自考教材
    @Inject
    VideoCourseAdapter mVideoCourseAdapter;//精品课
    @Inject
    VideoLiveAdapter mVideoLiveAdapter;//直播

    @Inject
    public RemedialClassPresenter(RemedialClassContract.Model model, RemedialClassContract.View rootView) {
        super(model, rootView);
    }

    public void getDataList(boolean update, boolean showDialog) {
        int user_id;
        Observable<RemediaClassBean> mRemediaClassObservable;
        if (UserCache.getUserCache() != null) {
            user_id = UserCache.getUserCache().getUser_id();
            mRemediaClassObservable = mModel.getDataList(user_id, update);
        } else {
            mRemediaClassObservable = mModel.getDataList(update);
        }
        mRemediaClassObservable
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if (showDialog)
                        mRootView.showLoadingDialog();
                    else
                        mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> {
                    if (showDialog)
                        mRootView.hideLoadingDialog();
                    else
                        mRootView.hideLoading();
                })
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.DESTROY_VIEW))
                .subscribe(new ErrorHandleSubscriber<RemediaClassBean>(mErrorHandler) {
                    @Override
                    public void onNext(RemediaClassBean response) {
                        if (response.isSuccess()) {
                            mVideoLiveAdapter.setNewData(response.getData().getLive());
                            mClassAdapter.setNewData(response.getData().getClassroom_list());
                            mVideoCourseAdapter.setNewData(response.getData().getCourses_list());
                            mBookAdapter.setNewData(response.getData().getBookList());
                            mRootView.setBibleAdapterData(response.getData().getGoodsList());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void liveAppointment() {
        int liveId = mVideoLiveAdapter.getItem(mRootView.getPosition()).getId();
        mModel.liveAppointment(liveId)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            VideoLiveBean bean = mVideoLiveAdapter.getItem(mRootView.getPosition());
                            bean.setState(bean.getState() == 2 ? 3 : 2);
                            bean.setCount(bean.getState() == 3 ? bean.getCount() - 1 : bean.getCount() + 1);
                            mVideoLiveAdapter.notifyItemChanged(mRootView.getPosition());
                        }
                    }
                });
    }

    public void liveReplay() {
        int liveId = mVideoLiveAdapter.getItem(mRootView.getPosition()).getId();
        mModel.replay(liveId)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<VideoLiveBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<VideoLiveBean> s) {
                        if (s.isSuccess()) {
                            VideoLiveBean bean = s.getData();
                            LiveHelper.openReplayRoom(mRootView.getActivity(), bean.getRoom_id(), bean.getLive_Id(), bean.getPlayback_id());
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }
}
