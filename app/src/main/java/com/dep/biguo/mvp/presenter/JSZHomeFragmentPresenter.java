package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CourseGroupBean;
import com.dep.biguo.bean.HomeBean;
import com.dep.biguo.bean.jsz.JSZProvinceBean;
import com.dep.biguo.mvp.contract.JSZHomeFragmentContract;
import com.dep.biguo.mvp.ui.adapter.HomeArticleAdapter;
import com.dep.biguo.mvp.ui.adapter.HomeMenuAdapter;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.FragmentEvent;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;


/**
 * ================================================
 * Description:
 * ================================================
 */
@FragmentScope
public class JSZHomeFragmentPresenter extends BasePresenter<JSZHomeFragmentContract.Model, JSZHomeFragmentContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;
    @Inject
    HomeArticleAdapter mArticleAdapter;
    @Inject
    HomeMenuAdapter mHomeMenuAdapter;

    @Inject
    public JSZHomeFragmentPresenter(JSZHomeFragmentContract.Model model, JSZHomeFragmentContract.View rootView) {
        super(model, rootView);
    }

    public void getHomeData(boolean update, boolean showDialog, int province_id) {
        String code = "c0003";
        CourseGroupBean.CourseBean course = UserCache.getCourse();
        if (course != null)
            code = course.getCode();
        Observable<BaseResponse<HomeBean>> response = null;

        if (province_id == -1) response = mModel.getHomeData(code, update);
        else response = mModel.getHomeData(code, update, province_id);

        response.subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> {
                    if (showDialog)
                        mRootView.showLoadingDialog();
                    else
                        mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> {
                    if (showDialog)
                        mRootView.hideLoadingDialog();
                    else
                        mRootView.hideLoading();
                })
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.DESTROY_VIEW))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<HomeBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<HomeBean> response) {
                        if (response.isSuccess()) {
                            mRootView.setBannerData(response.getData().getSwiper());
                            mRootView.setAdvertisingData(response.getData().getAdvertising());

                            mArticleAdapter.setNewData(response.getData().getFindings());

                            List<HomeBean.MenuBean> menuData = new ArrayList<>();
                            String vipImg = "";
                            String secretImg = "";
                            for (HomeBean.MenuBean bean : response.getData().getIcon()) {

                                if (bean.getType() == 12) {
                                    vipImg = bean.getImg();
                                } else if (bean.getType() == 13) {
                                    secretImg = bean.getImg();
                                } else {
                                    menuData.add(bean);
                                }
                            }
                            mRootView.setVipSecretData(vipImg, secretImg);
                            if (menuData.size()%3!=0){
                                menuData.add(new HomeBean.MenuBean());//用于补充
                            }
                            mHomeMenuAdapter.setNewData(menuData);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void getCourse(boolean update) {
        JSZProvinceBean bean = UserCache.getJSZTestType();
        if (bean == null) return;
        mModel.getCourse(bean.getId(), update)
                .subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<CourseGroupBean>(mErrorHandler) {
                    @Override
                    public void onNext(CourseGroupBean response) {
                        if (response.isSuccess()) {
                            mRootView.setCourseData(response);
                        }
                    }
                });
    }

    public void editCourse(int course_id) {
        mModel.editCourse(course_id)
                .subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            getCourse(true);
                        }
                    }
                });
    }

    public void editScore(String code, String score) {
        mModel.editScore(code, score)
                .subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            getCourse(true);
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }
}
