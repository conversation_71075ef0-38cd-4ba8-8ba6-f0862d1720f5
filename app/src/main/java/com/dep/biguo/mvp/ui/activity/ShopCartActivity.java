package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.di.component.DaggerShopCartComponent;
import com.dep.biguo.mvp.contract.ShopCartContract;
import com.dep.biguo.mvp.presenter.ShopCartPresenter;
import com.dep.biguo.mvp.ui.adapter.ShopCartAdapter;
import com.dep.biguo.mvp.ui.adapter.ShopCartIntegratedAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.SpannableUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.widget.ItemDecoration;
import com.dep.biguo.widget.loadsir.EmptyContentCallBack;
import com.dep.biguo.widget.loadsir.ErrorCallBack;
import com.dep.biguo.widget.loadsir.LoadingCallBack;
import com.dep.biguo.widget.loadsir.NetworkCallBack;
import com.google.gson.Gson;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.di.component.AppComponent;
import com.kingja.loadsir.callback.Callback;
import com.kingja.loadsir.core.LoadService;
import com.kingja.loadsir.core.LoadSir;

import org.simple.eventbus.Subscriber;

import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

import static com.jess.arms.utils.Preconditions.checkNotNull;

public class ShopCartActivity extends BaseActivity<ShopCartPresenter> implements ShopCartContract.View {

    public static final String EXTRA_EDIT_MODE = "extra_edit_mode";
    public static final String EVENT_INVALID_ITEMS_DELETED = "event_invalid_items_deleted";

    public static void start(Context context, boolean editMode){
        Intent intent = new Intent(context, ShopCartActivity.class);
        intent.putExtra(EXTRA_EDIT_MODE, editMode);
        context.startActivity(intent);
    }


    @BindView(R.id.recy_cart)
    RecyclerView recyCart;
    @BindView(R.id.iv_select_all)
    ImageView ivSelectAll;
    @BindView(R.id.tv_money)
    TextView tvMoney;
    @BindView(R.id.tv_buy)
    TextView tvBuy;
    @BindView(R.id.tv_delete)
    TextView tvDelete;
    @BindView(R.id.amountLayout)
    ConstraintLayout amountLayout;
    @BindView(R.id.tv_postprice)
    TextView tvPostprice;
    @BindView(R.id.tv_selected)
    TextView tvSelected;
    @BindView(R.id.invalidItemsLayout)
    View invalidItemsLayout;
    @BindView(R.id.tvInvalidItemsTip)
    TextView tvInvalidItemsTip;
    private LinearLayout invalidItemsContainer;

    // 移除自定义 ToolBar，改为使用布局头部的编辑按钮
    private TextView tvEditHeaderRef;

    // 使用单个适配器，内部处理分类标题和商品项
    private ShopCartIntegratedAdapter cartAdapter;
    private List<ShopBean> mData; // 全量数据（用于统计、提交）

    private String goods_ids = ""; //要删除的商品id

    private boolean isEdit = false;//是否为编辑状态

    private double postPrice;//邮费

    private LoadService mLoadService;

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerShopCartComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        return R.layout.shop_cart_activity;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        // 取消 ToolBar，标题和编辑操作由布局头部承担

        // 如果通过 Intent 指定了进入编辑模式，则默认切到编辑态
        boolean startInEdit = getIntent().getBooleanExtra(EXTRA_EDIT_MODE, false);
        if (startInEdit) {
            isEdit = false; // force toggle into edit
            toggleEditMode();
        }
        
        // 注册EventBus监听器，用于接收InvalidItemsActivity的返回结果
        // 当失效商品被删除后，需要刷新购物车页面
        mLoadService = LoadSir.getDefault().register(this, (Callback.OnReloadListener) v -> mPresenter.getCartData());
        
        // 注册EventBus
        org.simple.eventbus.EventBus.getDefault().register(this);

        mData = new ArrayList<>();
        // 先给 RecyclerView 设置 LayoutManager
        recyCart.setLayoutManager(new LinearLayoutManager(this));

        // 初始化单个适配器
        cartAdapter = new ShopCartIntegratedAdapter();
        recyCart.setAdapter(cartAdapter);
        // 移除分隔线装饰器，让商品项紧密连接
        // recyCart.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setLeftMargin(10).setRightMargin(10));

        // 注意：头部已通过 XML include 到页面，不再通过 addHeaderView 添加
        View header = findViewById(R.id.cartHeader);
        // 返回与编辑
        header.findViewById(R.id.ivBackHeader).setOnClickListener(v -> finish());
        tvEditHeaderRef = header.findViewById(R.id.tvEditHeader);
        if (tvEditHeaderRef != null) {
            tvEditHeaderRef.setOnClickListener(v -> toggleEditMode());
            tvEditHeaderRef.setText(isEdit ? "完成" : "编辑");
        }

        // Tabs 点击事件
        header.findViewById(R.id.tvTabCart).setOnClickListener(v -> { /* 当前页，无动作 */ });
        header.findViewById(R.id.tvTabOrder).setOnClickListener(v -> {
            if (!MainAppUtils.checkLogin(this)) return;
            ShopOrderSimpleActivity.start(this);
        });
        header.findViewById(R.id.tvTabIdle).setOnClickListener(v -> {
            if (!MainAppUtils.checkLogin(this)) return;
            try {
                PublishIdleActivity.class.getName();
                startActivity(new android.content.Intent(this, PublishIdleActivity.class));
            } catch (Throwable t) {
                android.widget.Toast.makeText(this, "功能开发中", android.widget.Toast.LENGTH_SHORT).show();
            }
        });
        // 搜索与地址（占位交互）
        header.findViewById(R.id.tvSearch).setOnClickListener(v -> {
            android.widget.Toast.makeText(this, "搜索功能开发中", android.widget.Toast.LENGTH_SHORT).show();
        });
        header.findViewById(R.id.ivAddress).setOnClickListener(v -> {
            android.widget.Toast.makeText(this, "选择地址功能开发中", android.widget.Toast.LENGTH_SHORT).show();
        });

        // 绑定点击事件
        ShopCartIntegratedAdapter.OnItemChildClickListener childClick = (adapter, v, position) -> {
            switch (v.getId()) {
                case R.id.tv_reduce:
                    cartAdapter.reduce(position);
                    break;
                case R.id.tv_add:
                    cartAdapter.add(position);
                    break;
            }
        };
        ShopCartIntegratedAdapter.OnItemClickListener itemClick = (adapter, view, position) -> {
            cartAdapter.selectPosition(position);
            setSelectedStatus();
        };

        ShopCartIntegratedAdapter.DoAddAndReduceListener addReduceListener = new ShopCartIntegratedAdapter.DoAddAndReduceListener() {
            @Override public void onAdd() { setSelectedStatus(); }
            @Override public void onReduce() { setSelectedStatus(); }
        };
        
        cartAdapter.setOnItemClickListener(itemClick);
        cartAdapter.setOnItemChildClickListener(childClick);
        cartAdapter.setDoAddAndReduceListener(addReduceListener);
        
        
        // 初始化失效商品区域
        initInvalidItemsSection();
        
        // 初始化失效商品容器
        invalidItemsContainer = invalidItemsLayout.findViewById(R.id.invalid_items_container);
    }
    private List<ShopBean> getAllSelected(){
        return cartAdapter.getSelectedItems();
    }


    private void toggleEditMode(){
        if (AppUtil.isEmpty(mData)) return;
        isEdit = !isEdit;
        if (tvEditHeaderRef != null) tvEditHeaderRef.setText(isEdit ? "完成" : "编辑");
        tvBuy.setVisibility(isEdit ? View.GONE : View.VISIBLE);
        tvDelete.setVisibility(isEdit ? View.VISIBLE : View.GONE);
        amountLayout.setVisibility(isEdit ? View.GONE : View.VISIBLE);
        setResult(RESULT_OK);
    }


    @OnClick({R.id.rlay_select_all, R.id.tv_buy, R.id.tv_delete})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.rlay_select_all:
                // 切换为全选/全不选
                boolean selectAll = !cartAdapter.isSelectAll();
                cartAdapter.selectAll(selectAll);
                setSelectedStatus();
            break;
            case R.id.tv_buy: //立即购买
                List<ShopBean> selected = getAllSelected();
                if (AppUtil.isEmpty(selected)) return;

                List<Map<String,String>> goodsInfoList = new ArrayList<>();
                for(ShopBean bean : selected){
                    Map<String,String> goodsInfoMap = new HashMap<>();
                    goodsInfoMap.put(PayParamsBean.GOODS_IDS,bean.getId()+"");
                    goodsInfoMap.put(PayParamsBean.COUNT,bean.getCount()+"");
                    goodsInfoMap.put(PayParamsBean.LAYER_ID, UserCache.getProfession().getLayer()+"");
                    goodsInfoMap.put(PayParamsBean.PROFESSIONS_ID, UserCache.getProfession().getId()+"");

                    goodsInfoList.add(goodsInfoMap);
                }
                Gson gson = new Gson();
                Map<String,Object> map = new HashMap<>();
                map.put(PayParamsBean.TYPE,PayUtils.BOOK);
                map.put(PayParamsBean.GOODS_INFO,gson.toJson(goodsInfoList));
                OrderPayActivity.start(this,map);
                break;
            case R.id.tv_delete: //删除
                // 更新goods_ids变量以确保获取最新的选中商品ID
                setSelectedStatus();
                List<ShopBean> selectedItems = getAllSelected();
                if (selectedItems.size() == mData.size() && !mData.isEmpty()) {
                    mPresenter.clearCart();
                } else {
                    mPresenter.deleteCart();
                }
                break;
        }
    }


    @Override
    public void showLoading() {
        mLoadService.showCallback(LoadingCallBack.class);
    }

    @Override
    public void hideLoading() {
        mLoadService.showSuccess();
    }

    @Override
    public void showEmptyView() {
        mLoadService.showCallback(EmptyContentCallBack.class);
    }

    @Override
    public void showErrorView(Throwable e) {
        if (e instanceof UnknownHostException)
            mLoadService.showCallback(NetworkCallBack.class);
        else
            mLoadService.showCallback(ErrorCallBack.class);

        TextView refreshView = mLoadService.getLoadLayout().findViewById(R.id.refreshView);
        refreshView.setOnClickListener(view -> mPresenter.getCartData());
    }

    @Override
    public void showMessage(@NonNull String message) {
        checkNotNull(message);
        ToastUtils.show(message);
    }

    /**
     * 设置选择后的状态
     */

    private void setSelectedStatus() {
        // 全选按钮状态
        boolean allSelect = cartAdapter.isSelectAll();
        ivSelectAll.setImageResource(allSelect ? R.drawable.opinion_select : R.drawable.opinion_normal_v2);

        // 统计选中项
        List<ShopBean> shops = getAllSelected();
        tvDelete.setText(String.format("删除(%s)", shops.size()));

        // 结算按钮
        tvBuy.setAlpha(!shops.isEmpty() ? 1f : 0.6f);
        tvBuy.setEnabled(!shops.isEmpty());
        tvBuy.setText(String.format("去结算(%s)", shops.size()));

        // 合计金额
        double price = 0;
        postPrice = 0;
        goods_ids = "";
        int number = 0; //数量
        for (ShopBean shop : shops) {
            double unit = Float.parseFloat(shop.getPreferential_price()) == 0 ? Float.parseFloat(shop.getPrice()) : Float.parseFloat(shop.getPreferential_price());
            price += unit * shop.getCount();
            goods_ids += shop.getId() + "_";
            number += shop.getCount();
            postPrice = Math.max(Float.parseFloat(shop.getPostage()), postPrice);
        }
        tvMoney.setText(String.format("¥%s", price + postPrice));
        tvMoney.setGravity(Gravity.CENTER_VERTICAL);
        if (number < 3 && postPrice > 0) {
            tvPostprice.setText(String.format("含邮费：%s元", postPrice));
        }
    }

    @Override
    public String getGoods_ids() {
        if (TextUtils.isEmpty(goods_ids)) {
            return goods_ids;
        }
        return goods_ids.substring(0, goods_ids.length() - 1);
    }

    @Override
    public String getCounts() {
        String counts = "";
        for (int i = 0, size = mData.size(); i < size; i++) {
            counts += mData.get(i).getCount() + "_";
        }
        if (TextUtils.isEmpty(counts)) {
            return counts;
        }
        return counts.substring(0, counts.length() - 1);
    }

    // 将接口返回的购物车列表设置到适配器
    private void bindSections(List<ShopBean> list){
        cartAdapter.setData(list);
    }

    @Override
    public String getAllGoods_ids() {
        String goods_ids = "";
        for (int i = 0, size = mData.size(); i < size; i++) {
            goods_ids += mData.get(i).getId() + "_";
        }
        if (TextUtils.isEmpty(goods_ids)) {
            return goods_ids;
        }
        return goods_ids.substring(0, goods_ids.length() - 1);
    }

    @Override
    public void setCartData(List<ShopBean> data) {
        mData.clear();
        mData.addAll(data);
        bindSections(data);
        
        // 更新失效商品区域的显示
        List<ShopBean> invalidItems = cartAdapter.getInvalidItems();
        android.util.Log.d("AddToCart", "ShopCartActivity.setCartData - Invalid items count: " + (invalidItems != null ? invalidItems.size() : 0));
        if (invalidItems != null && !invalidItems.isEmpty()) {
            android.util.Log.d("AddToCart", "ShopCartActivity.setCartData - Showing invalid items section");
            invalidItemsLayout.setVisibility(View.VISIBLE);
            tvInvalidItemsTip.setVisibility(View.GONE);
            
            // 清空之前的失效商品视图
            invalidItemsContainer.removeAllViews();
            
            // 添加失效商品到容器中
            for (ShopBean item : invalidItems) {
                View itemView = getLayoutInflater().inflate(R.layout.item_invalid_shop_cart_single, invalidItemsContainer, false);
                bindInvalidItem(itemView, item);
                invalidItemsContainer.addView(itemView);
            }
            
            // 为"清空"按钮设置点击事件
            TextView tvClearInvalid = invalidItemsLayout.findViewById(R.id.tv_clear_invalid);
            if (tvClearInvalid != null) {
                tvClearInvalid.setOnClickListener(v -> {
                    // 跳转到失效商品页面，让用户选择要删除的失效商品
                    InvalidItemsActivity.start(ShopCartActivity.this, new ArrayList<>(invalidItems));
                });
            }
        } else {
            android.util.Log.d("AddToCart", "ShopCartActivity.setCartData - Hiding invalid items section");
            invalidItemsLayout.setVisibility(View.GONE);
            tvInvalidItemsTip.setVisibility(View.GONE);
        }
    }

    @Subscriber(tag = EventBusTags.CREATE_BOOK_ORDER_SUCCESS)
    private void createSuccessToUpdateCart(Object obj) {
        if (mPresenter == null) return;
        mPresenter.getCartData();
        setResult(RESULT_OK);
    }

    @Subscriber(tag = EventBusTags.PAY_SUCCESS)
    private void PaySuccess(int code) {
        cartAdapter.selectAll(true);
        mPresenter.getCartData();
    }

    @Override
    public void killMyself() {
        finish();
    }

    @Override
    public void onBackPressed() {
        if (AppUtil.isEmpty(mData)) killMyself();
        else mPresenter.editCart();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 取消注册EventBus
        org.simple.eventbus.EventBus.getDefault().unregister(this);
    }
    
    private void initInvalidItemsSection() {
        // 失效商品区域现在直接显示失效商品，不需要点击跳转
    }
    
    @Subscriber(tag = EVENT_INVALID_ITEMS_DELETED)
    private void onInvalidItemsDeleted(Object obj) {
        // 当失效商品被删除后，需要刷新购物车页面
        mPresenter.getCartData();
    }
    
    private void bindInvalidItem(View itemView, ShopBean item) {
        android.widget.ImageView ivSelect = itemView.findViewById(R.id.iv_select);
        android.widget.ImageView ivCover = itemView.findViewById(R.id.iv_cover);
        android.widget.TextView tvTitle = itemView.findViewById(R.id.tv_title);
        android.widget.TextView tvPrice = itemView.findViewById(R.id.tv_price);
        android.widget.TextView tvTag = itemView.findViewById(R.id.tv_tag);
        android.widget.TextView tvSpec = itemView.findViewById(R.id.tv_spec);
        
        tvTitle.setText(item.getName());
        
        String priceText = "¥ " + (Float.parseFloat(item.getPreferential_price()) == 0 ?
                item.getPrice() : item.getPreferential_price());
        tvPrice.setText(priceText);
        
        // 标签
        String tagText = item.getTag();
        if (tagText == null || tagText.length() == 0) {
            if ("助农项目".equals(item.getCategory())) tagText = "助农";
            else if ("二手教材".equals(item.getCategory())) tagText = "二手";
            else tagText = "";
        }
        tvTag.setText(tagText);
        
        // 规格
        tvSpec.setText(item.getDescribe() == null ? "" : item.getDescribe());
        
        // 失效商品的UI处理
        tvTitle.setTextColor(itemView.getContext().getResources().getColor(R.color.tblack3));
        tvPrice.setTextColor(itemView.getContext().getResources().getColor(R.color.theme));
        // 选择框（列表页仍可勾选用于批量删除）
        ivSelect.setImageResource(item.getIs_select() ?
                R.drawable.opinion_select : R.drawable.opinion_normal_v2);
        
        // 为checkbox设置点击事件
        ivSelect.setOnClickListener(v -> {
            item.setIs_select(!item.getIs_select());
            ivSelect.setImageResource(item.getIs_select() ?
                    R.drawable.opinion_select : R.drawable.opinion_normal_v2);
        });
        
        // 设置图片（这里简化处理，实际项目中可能需要使用图片加载库）
        // Glide.with(this).load(item.getImg()).into(ivCover);
    }
}
