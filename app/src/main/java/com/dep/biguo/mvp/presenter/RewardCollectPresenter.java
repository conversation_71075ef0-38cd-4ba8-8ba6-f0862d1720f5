package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.RewardCollectBean;
import com.dep.biguo.mvp.contract.RewardCollectContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class RewardCollectPresenter extends BasePresenter<RewardCollectContract.Model, RewardCollectContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private List<ProvinceBean> provinceBeanList;

    @Inject
    public RewardCollectPresenter(RewardCollectContract.Model model, RewardCollectContract.View rootView) {
        super(model, rootView);
    }

    public void getRewardCollectData(){
        mModel.getRewardCollectData()
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<RewardCollectBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<RewardCollectBean> response) {
                        if (response.isSuccess()) {
                            mRootView.showSuccessView();
                            mRootView.getRewardCollectDataSuccess(response.getData());
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void getProvince(){
        if(!AppUtil.isEmpty(provinceBeanList)){
            mRootView.showSelectProvinceDialog(provinceBeanList);
            return;
        }

        mModel.getProvince()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<ProvinceBean>(mErrorHandler) {
                    @Override
                    public void onNext(ProvinceBean response) {
                        if (response.isSuccess()) {
                            provinceBeanList = response.getData();
                            mRootView.showSelectProvinceDialog(provinceBeanList);
                        }
                    }
                });
    }

    public void commit(String title, int provinceId, String desc, String filePath, String url, int url_type){
        mModel.commit(title, provinceId, desc, filePath, url, url_type)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if(response.isSuccess()) {
                            mRootView.commitSuccess();
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
