package com.dep.biguo.mvp.ui.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;

import com.dep.biguo.R;
import com.dep.biguo.bean.GroupCommentBean;
import com.dep.biguo.databinding.SecondHandDetailActivityBinding;
import com.dep.biguo.di.component.DaggerProductDetailStandaloneComponent;
import com.dep.biguo.mvp.contract.ProductDetailStandaloneContract;
import com.dep.biguo.mvp.presenter.ProductDetailStandalonePresenter;
import com.dep.biguo.mvp.ui.adapter.GroupCommentAdapter;
import com.dep.biguo.utils.BannerRoundImageLoader;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.html.HtmlUtil;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.imp.OnPageChangeImp;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.common.Constant;
import com.umeng.socialize.ShareAction;
import com.umeng.socialize.UMShareAPI;
import com.umeng.socialize.UMShareListener;
import com.umeng.socialize.bean.SHARE_MEDIA;
import com.umeng.socialize.media.UMImage;
import com.umeng.socialize.media.UMWeb;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;
import com.dep.biguo.utils.TestCartManager;
import com.dep.biguo.utils.TestConfig;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 二手商品详情页
 * 要求：
 * - banner布局参考 MallHomeNewFragment 的圆角视觉，使用 Banner + BannerRoundImageLoader 实现
 * - 评论模块直接复用项目里的“考友点评”适配器 GroupCommentAdapter
 */
public class SecondHandDetailActivity extends BaseLoadSirActivity<ProductDetailStandalonePresenter>
        implements ProductDetailStandaloneContract.View, View.OnClickListener {

    public static final String EXTRA_GOODS_ID = "extra_goods_id";
    public static final String EXTRA_TITLE = "extra_title";
    public static final String EXTRA_INTRO_HTML = "extra_intro_html";
    public static final String EXTRA_COMMENT_COUNT = "extra_comment_count";

    // 二手专属可选字段
    public static final String EXTRA_PRICE = "extra_price";                 // e.g. ¥16.00
    public static final String EXTRA_ORIGINAL_PRICE = "extra_original_price";// e.g. ¥29.00
    public static final String EXTRA_CONDITION = "extra_condition";          // e.g. 9成新
    public static final String EXTRA_CATEGORY_TAG = "extra_category_tag";    // e.g. 二手教材
    public static final String EXTRA_IMAGE_URLS = "extra_image_urls";        // ArrayList<String>

    private SecondHandDetailActivityBinding binding;
    private int goodsId;
    private int initCommentCount;
    private ActivityResultLauncher<Intent> addressPickerLauncher;

    private GroupCommentAdapter commentAdapter;
    // 暂存当前弹窗引用，以便地址选择返回后回填
    private com.dep.biguo.dialog.AddToCartDialog pendingDialogForAddress;


    public static void start(Context context, int goodsId, String title, String introHtml, int commentCount){
        Intent intent = new Intent(context, SecondHandDetailActivity.class);
        intent.putExtra(EXTRA_GOODS_ID, goodsId);
        intent.putExtra(EXTRA_TITLE, title);
        intent.putExtra(EXTRA_INTRO_HTML, introHtml);
        intent.putExtra(EXTRA_COMMENT_COUNT, commentCount);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerProductDetailStandaloneComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.second_hand_detail_activity);
        binding.setOnClickListener(this);
        // 预注册地址选择器，避免在 RESUMED 状态注册造成崩溃
        addressPickerLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
            if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                com.dep.biguo.bean.AddressBean addr = result.getData().getParcelableExtra(com.dep.biguo.mvp.ui.activity.AddressMsgActivity.ADDRESS);
                if (pendingDialogForAddress != null) {
                    pendingDialogForAddress.bindAddress(addr);
                }
            }
        });

        return 0;
    }

    @Override
    public View initLoadSir() { return binding.getRoot(); }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        // 状态栏
        StatusBarHelper.setStatusBarColor(this, ContextCompat.getColor(this, R.color.tran));
        if (binding.topBar != null) {
            StatusBarHelper.setStatusbarColorView(this, binding.topBar);
        }

        goodsId = getIntent().getIntExtra(EXTRA_GOODS_ID, 0);
        initCommentCount = getIntent().getIntExtra(EXTRA_COMMENT_COUNT, 0);
        String title = getIntent().getStringExtra(EXTRA_TITLE);
        String html = getIntent().getStringExtra(EXTRA_INTRO_HTML);
        if (title == null || title.length() == 0) title = "马克思主义二";
        if (html == null || html.length() == 0) html = "<p>这是对商品的描述。这是对商品的描述这是对商品的描述。</p>";
        if (initCommentCount == 0) initCommentCount = 12;

        setupHeader(title);

        // 评论（使用无星级+无更多按钮的样式）
        commentAdapter = new GroupCommentAdapter(R.layout.second_hand_comment_item, new ArrayList<>());
        binding.commentRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        commentAdapter.bindToRecyclerView(binding.commentRecyclerView);

        // 移除：详情介绍模块（HTML+配图）已在布局中删除

        setCommentCountTitle(initCommentCount);
        loadTestComments();
    }

    @Override
    public void onRequest() { showSuccessView(); }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.allCommentView) {
            GroupCommentActivity.start(this, 0, String.valueOf(goodsId), 0, PayUtils.BOOK);
        } else if (id == R.id.backView) {
            finish();
        } else if (id == R.id.shareView) {
            doShare();
        } else if (id == R.id.addCartButton) {
            // 二手教材直接加入购物车，不显示弹窗
            directAddToCart();
        }
    }

    private void showAddCartDialog(){
        com.dep.biguo.dialog.AddToCartDialog dialog = new com.dep.biguo.dialog.AddToCartDialog(this);
        // 暂存弹窗引用，供地址选择返回回填
        pendingDialogForAddress = dialog;
        dialog.setOnAddressClickListener(() -> {
            com.dep.biguo.mvp.ui.activity.AddressListActivity.Start(this, addressPickerLauncher);
        });
        dialog.setOnConfirmListener(count -> {
            android.util.Log.d("SecondHandDetail", "onConfirm add-to-cart clicked: goodsId=" + goodsId + ", count=" + count);
            if (!com.dep.biguo.utils.MainAppUtils.checkLogin(this)) return;
            if (goodsId > 0) {
                // 本地测试数据下，强制将该商品归类为“二手教材”
                if (TestConfig.USE_LOCAL_TEST_DATA) {
                    TestCartManager.getInstance().overrideCategoryForGoods(goodsId, "二手教材");
                }
                android.util.Log.d("SecondHandDetail", "calling presenter.addShopCart(" + goodsId + ")");
                mPresenter.addShopCart(goodsId);
            } else {
                com.jess.arms.utils.ArmsUtils.snackbarText("商品信息异常");
            }
        });

        // mock data
        java.util.ArrayList<com.dep.biguo.dialog.AddToCartDialog.ColorItem> colors = new java.util.ArrayList<>();
        colors.add(new com.dep.biguo.dialog.AddToCartDialog.ColorItem("灰色", R.drawable.img_banner_placeholder));
        colors.add(new com.dep.biguo.dialog.AddToCartDialog.ColorItem("银色", R.drawable.banner_side_left));
        colors.add(new com.dep.biguo.dialog.AddToCartDialog.ColorItem("蓝色", R.drawable.banner_side_right));
        dialog.setColors(colors);

        java.util.ArrayList<String> bundles = new java.util.ArrayList<>();
        bundles.add("128GB+16GB+标准版");
        bundles.add("256GB+16GB+官方版");
        bundles.add("256GB+16GB+官方版");
        dialog.setBundles(bundles);
        dialog.show();
    }
    
    // 直接加入购物车（不显示弹窗）
    private void directAddToCart() {
        android.util.Log.d("AddToCart", "===== SecondHandDetailActivity: Direct add to cart (no dialog) =====");
        android.util.Log.d("AddToCart", "Current goodsId: " + goodsId);
        android.util.Log.d("AddToCart", "This is a second-hand book");
        
        if (!com.dep.biguo.utils.MainAppUtils.checkLogin(this)) {
            android.util.Log.d("AddToCart", "User not logged in, showing login dialog");
            return;
        }
        
        if (goodsId > 0) {
            // 本地测试数据下，强制将该商品归类为“二手教材”
            if (TestConfig.USE_LOCAL_TEST_DATA) {
                TestCartManager.getInstance().overrideCategoryForGoods(goodsId, "二手教材");
            }
            android.util.Log.d("AddToCart", "Valid goods ID, calling presenter.addShopCart(" + goodsId + ")");
            mPresenter.addShopCart(goodsId);
        } else {
            android.util.Log.e("AddToCart", "Invalid goods ID: " + goodsId);
            com.jess.arms.utils.ArmsUtils.snackbarText("商品信息异常");
        }
    }

        private void doShare() {
            try {
                String title = getIntent().getStringExtra(EXTRA_TITLE);
                if (title == null || title.length() == 0) title = "二手教材";
                String desc = "好物转让，快来看看~";
                // 深链：走自有 Scheme，便于 App 内跳转
                String url = "https://www.biguotk.com/share?" +
                        "action=" + com.dep.biguo.utils.StartFinal.DETAIL +
                        "&type=skill_video" +
                        "&source_type=0" +
                        "&product_id=" + goodsId;

                UMWeb web = new UMWeb(url);
                web.setTitle(title);
                web.setDescription(desc);
                UMImage thumb = new UMImage(this, R.drawable.img_banner_placeholder);
                web.setThumb(thumb);

                new ShareAction(this)
                        .withMedia(web)
                        .setDisplayList(SHARE_MEDIA.WEIXIN, SHARE_MEDIA.WEIXIN_CIRCLE, SHARE_MEDIA.QQ)
                        .setCallback(umShareListener)
                        .open();
            } catch (Throwable t) {
                ArmsUtils.snackbarText("分享初始化失败");
            }
        }

        private final UMShareListener umShareListener = new UMShareListener() {
            @Override public void onStart(SHARE_MEDIA share_media) {}
            @Override public void onResult(SHARE_MEDIA share_media) { ArmsUtils.snackbarText("分享成功"); }
            @Override public void onError(SHARE_MEDIA share_media, Throwable throwable) { ArmsUtils.snackbarText("分享失败"); }
            @Override public void onCancel(SHARE_MEDIA share_media) { ArmsUtils.snackbarText("已取消"); }
        };

        @Override
        protected void onActivityResult(int requestCode, int resultCode, Intent data) {
            super.onActivityResult(requestCode, resultCode, data);
            UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data);
        }


    @Override
    public void showComments(List<GroupCommentBean> comments) {
        commentAdapter.setNewData(comments);
        binding.allCommentView.setVisibility((comments == null || comments.size() <= 2) && initCommentCount <= 2 ? View.GONE : View.VISIBLE);
    }

    private void setupHeader(String title){
        TextView headerTitle = findViewById(R.id.headerTitleView);
        if (headerTitle != null) headerTitle.setText(title);

        // 价格与标签
        TextView priceView = findViewById(R.id.priceView);
        TextView originalPriceView = findViewById(R.id.originalPriceView);
        TextView conditionTag = findViewById(R.id.conditionTagView);
        TextView categoryTag = findViewById(R.id.categoryTagView);

        String price = getIntent().getStringExtra(EXTRA_PRICE);
        String oldPrice = getIntent().getStringExtra(EXTRA_ORIGINAL_PRICE);
        String condition = getIntent().getStringExtra(EXTRA_CONDITION);
        String category = getIntent().getStringExtra(EXTRA_CATEGORY_TAG);

        if (priceView != null) priceView.setText(price == null ? "¥16.00" : price);
        if (originalPriceView != null) {
            originalPriceView.setText(oldPrice == null ? "原价¥29.00" : ("原价" + oldPrice));
            originalPriceView.getPaint().setFlags(android.graphics.Paint.STRIKE_THRU_TEXT_FLAG | android.graphics.Paint.ANTI_ALIAS_FLAG);
        }
        if (conditionTag != null) conditionTag.setText(condition == null ? "9成新" : condition);
        if (categoryTag != null) categoryTag.setText(category == null ? "二手教材" : category);

        // 轮播：对齐 ProductDetailStandaloneActivity 的实现
        com.youth.banner.Banner banner = findViewById(R.id.bannerView);
        TextView indexView = findViewById(R.id.bannerIndexView);
        if (banner != null) {
            ArrayList<String> images = getIntent().getStringArrayListExtra(EXTRA_IMAGE_URLS);
            if (images == null || images.isEmpty()) {
                images = new ArrayList<>(Arrays.asList(
                        "res://drawable/img_banner_placeholder",
                        "res://drawable/banner_side_left",
                        "res://drawable/banner_side_right",
                        "res://drawable/banner_side_back"
                ));
            }
            final ArrayList<String> finalImages = images;
            if (indexView != null) indexView.setText("1/" + finalImages.size());

            banner.setImages(images);
            banner.setImageLoader(new BannerRoundImageLoader());
            banner.setBannerStyle(com.youth.banner.BannerConfig.NOT_INDICATOR);
            banner.setOnPageChangeListener(new OnPageChangeImp() {
                @Override
                public void onPageSelected(int position) {
                    if (indexView != null) indexView.setText((position) + "/" + finalImages.size());
                }
            });
            banner.start();
        }
    }

    @Override
    public void setCommentCountTitle(int count) {
        binding.commentTitleView.setText("评论 (" + count + ")");
    }

    @Override public int getInitialCommentCount() { return initCommentCount; }
    @Override public int getGoodsId() { return goodsId; }
    @Override public Activity getActivity() { return this; }
    @Override public void showMessage(@NonNull String message) { ArmsUtils.snackbarText(message); }

    // Demo 数据
    private void loadTestComments() {
        List<GroupCommentBean> testComments = new ArrayList<>();
        GroupCommentBean c1 = new GroupCommentBean();
        c1.setId(1); c1.setUsers_id(1001); c1.setName("我是昵称"); c1.setAvatar("");
        c1.setComment("楼主可以便宜一点吗？"); c1.setCreated_at("3小时前"); c1.setRecommend(5);
        ArrayList<String> imgs = new ArrayList<>(); imgs.add("res://drawable/comment_placeholder_1"); c1.setImages(imgs);
        testComments.add(c1);

        GroupCommentBean c2 = new GroupCommentBean();
        c2.setId(2); c2.setUsers_id(1002); c2.setName("学习党"); c2.setAvatar("");
        c2.setComment("书本八成新，内容完整。"); c2.setCreated_at("1天前"); c2.setRecommend(4);
        testComments.add(c2);

        showComments(testComments);
    }
}

