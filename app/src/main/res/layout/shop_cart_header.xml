<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white"
    android:layout_marginTop="20dp"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="12dp"
    android:paddingBottom="8dp">

    <!-- 顶部一行：返回 | 分段 | 编辑 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/ivBackHeader"
            android:layout_width="7dp"
            android:layout_height="14dp"
            android:src="@drawable/arrow_back"
            android:background="?android:attr/selectableItemBackgroundBorderless"/>

        <LinearLayout
            android:id="@+id/segmentedTabs"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/bg_segment_container"
            android:padding="1dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:weightSum="3">

            <TextView
                android:id="@+id/tvTabCart"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:paddingTop="2dp"
                android:text="购物车"
                android:textSize="13sp"
                android:textColor="#D53E43"
                android:background="@drawable/bg_segment_selected_left"/>

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:alpha="0.6"
                android:background="#FFE6E6E6"/>

            <TextView
                android:id="@+id/tvTabOrder"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="订单"
                android:textSize="13sp"
                android:textColor="#333333"
                android:background="@android:color/transparent"/>

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:alpha="0.6"
                android:background="#FFE6E6E6"/>

            <TextView
                android:id="@+id/tvTabIdle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="我的闲置"
                android:textSize="13sp"
                android:textColor="#333333"
                android:background="@android:color/transparent"/>
        </LinearLayout>

        <TextView
            android:id="@+id/tvEditHeader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="编辑"
            android:textColor="#666666"
            android:textSize="12sp"/>
    </LinearLayout>

    <!-- 搜索与地址 -->
    <LinearLayout
        android:id="@+id/bar_search_address"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/tvSearch"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/bg_search_bar"
            android:drawableStart="@drawable/icon_search"
            android:drawablePadding="8dp"
            android:gravity="center_vertical"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="搜索关键字，查询题目"
            android:textColor="@android:color/darker_gray"/>

        <TextView
            android:id="@+id/ivAddress"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="2dp"
            android:drawableStart="@drawable/icon_location"
            android:drawablePadding="4dp"
            android:text="地址"
            android:textColor="#333333"
            android:textSize="12sp"/>
    </LinearLayout>

    <!-- 订单标签页 (初始隐藏) -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/orderTabLayout"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="12dp"
        android:background="@color/white"
        android:visibility="gone"
        app:tabMode="scrollable"
        app:tabGravity="start"
        app:tabIndicator="@drawable/tab_indicator_red"
        app:tabIndicatorGravity="bottom"
        app:tabIndicatorFullWidth="false"
        app:tabSelectedTextColor="@color/theme"
        app:tabTextColor="#999999"
        app:tabTextAppearance="@style/TabTextStyle"
        app:tabPaddingStart="16dp"
        app:tabPaddingEnd="16dp" />

</LinearLayout>

