package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.AliyunAuthBean;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.LiveBeegoBean;
import com.dep.biguo.bean.LivePlanListBean;
import com.dep.biguo.bean.LivePolyvBean;
import com.dep.biguo.live.LiveHelper;
import com.dep.biguo.mvp.contract.LivePlanListContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.FragmentEvent;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.annotations.NonNull;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class LivePlanListPresenter extends BasePresenter<LivePlanListContract.Model, LivePlanListContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public LivePlanListPresenter(LivePlanListContract.Model model, LivePlanListContract.View rootView) {
        super(model, rootView);
    }

    public void getLivePlanList(int product_id, int page) {
        mModel.getLivePlanList(product_id, page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<LivePlanListBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<LivePlanListBean>> s) {
                        if (s.isSuccess()) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.showEmptyView();
                                mRootView.getLiveSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getLiveSuccess(s.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                            mRootView.getLiveFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.getLiveFail();
                    }
                });
    }


    public void getPlayAuth(LivePlanListBean planBean, String video_id){
        mModel.getPlayAuth(video_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<AliyunAuthBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NonNull BaseResponse<AliyunAuthBean> response) {
                        if(response.isSuccess()){
                            mRootView.getPlayAuthSuccess(planBean, response.getData());
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
