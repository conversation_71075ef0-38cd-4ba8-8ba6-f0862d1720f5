package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.VipBean;
import com.dep.biguo.mvp.contract.IntegralConvertContract;
import com.dep.biguo.mvp.ui.adapter.IntegralConvertAdapter;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

@ActivityScope
public class IntegralConvertPresenter extends BasePresenter<IntegralConvertContract.Model, IntegralConvertContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    @Inject
    IntegralConvertAdapter mIntegralConvertAdapter;

    @Inject
    public IntegralConvertPresenter(IntegralConvertContract.Model model, IntegralConvertContract.View rootView) {
        super(model, rootView);
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    void onCreate() {
        getVipData(true);
    }

    public void getVipData(boolean showLoading) {
        mModel.getVip(UserCache.getProfession().getId())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if (showLoading)
                        mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> {
                    if (showLoading)
                        mRootView.hideLoading();
                })
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<VipBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<VipBean>> response) {
                        if (response.isSuccess()) {
                            mIntegralConvertAdapter.setNewData(response.getData());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void vipPoint() {
        VipBean bean = mIntegralConvertAdapter.getItem(mRootView.getSelectPosition());

        mModel.vipPoint(bean.getCode())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.showMessage(response.getResult_info());
                            getVipData(false);
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        this.mIntegralConvertAdapter = null;
        LoadingDialog.hideLoadingDialog();
    }
}
