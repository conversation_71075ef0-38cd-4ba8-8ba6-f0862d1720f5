package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.ReceiptTitleBean;
import com.dep.biguo.mvp.contract.ReceiptAddTitleContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class ReceiptAddTitlePresenter extends BasePresenter<ReceiptAddTitleContract.Model, ReceiptAddTitleContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public ReceiptAddTitlePresenter(ReceiptAddTitleContract.Model model, ReceiptAddTitleContract.View rootView) {
        super(model, rootView);
    }

    public void commitReceiptAdd(ReceiptTitleBean bean){
        Observable<BaseResponse> observable;
        if(bean.getId() > 0){
            observable = mModel.commitReceiptEdit(bean);
        }else {
            observable = mModel.commitReceiptAdd(bean);
        }

        observable.subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.commitReceiptAddSuccess();
                        }
                    }
                });
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
