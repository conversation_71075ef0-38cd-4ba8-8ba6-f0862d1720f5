package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.OnLifecycleEvent;

import com.dep.biguo.bean.VipCourseBean;
import com.dep.biguo.mvp.contract.VipAllCourseContract;
import com.dep.biguo.mvp.ui.adapter.VipCourseAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.ActivityEvent;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

import static androidx.lifecycle.Lifecycle.Event.ON_CREATE;


/**
 * ================================================
 * Description:
 * <p>
 * Created by MVPArmsTemplate on 07/01/2020 16:10
 * <a href="mailto:<EMAIL>">Contact me</a>
 * <a href="https://github.com/JessYanCoding">Follow me</a>
 * <a href="https://github.com/JessYanCoding/MVPArms">Star me</a>
 * <a href="https://github.com/JessYanCoding/MVPArms/wiki">See me</a>
 * <a href="https://github.com/JessYanCoding/MVPArmsTemplate">模版请保持更新</a>
 * ================================================
 */
@ActivityScope
public class VipAllCoursePresenter extends BasePresenter<VipAllCourseContract.Model, VipAllCourseContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;
    @Inject
    VipCourseAdapter mVipCourseAdapter;

    @Inject
    public VipAllCoursePresenter(VipAllCourseContract.Model model, VipAllCourseContract.View rootView) {
        super(model, rootView);
    }

    @OnLifecycleEvent(ON_CREATE)
    void getVipCourse() {
        getVipCourse(true,false);
    }

    public void getVipCourse(boolean showLoading,boolean isUpdate) {
        int ProfessionsId = 0;
        int ProvinceId = 0;
        if (UserCache.getUserCache() != null) {
            if (UserCache.getUserCache().getProfessions_id() > 0 && UserCache.getUserCache().getProvince_id() > 0) {
                ProfessionsId = UserCache.getUserCache().getProfessions_id();
                ProvinceId = UserCache.getUserCache().getProvince_id();
            }

        } else {
            ProfessionsId = UserCache.getProfession().getId();
            ProvinceId = UserCache.getProvince().getId();
        }
        mModel.getVipCourse(ProfessionsId, ProvinceId,isUpdate)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if (showLoading)
                        mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> {
                    if (showLoading)
                        mRootView.hideLoading();
                })
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<VipCourseBean>(mErrorHandler) {
                    @Override
                    public void onNext(VipCourseBean s) {
                        if (s.isSuccess()) {
                            if (AppUtil.isEmpty(s.getData())) {
                                mRootView.showEmptyView();
                            } else {
                                mVipCourseAdapter.setNewData(s.getData());
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        this.mVipCourseAdapter = null;
    }
}
