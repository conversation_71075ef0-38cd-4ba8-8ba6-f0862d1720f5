package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.OrderDetailBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.IndentDetailContract;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

@ActivityScope
public class IndentDetailPresenter extends BasePresenter<IndentDetailContract.Model, IndentDetailContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    private PayResultListener mPayListener;

    @Inject
    public IndentDetailPresenter(IndentDetailContract.Model model, IndentDetailContract.View rootView) {
        super(model, rootView);
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    void onCreate() {
        mPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.onSuccess();
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {

            }
        };

        getOrderDetail();
    }

    public void getOrderDetail() {
        mModel.getOrderDetail(mRootView.getOrderNumber())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<OrderDetailBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<OrderDetailBean> s) {
                        if (s.isSuccess()) {
                            mRootView.setOrderDetail(s.getData());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void cancelOrder() {
        mModel.cancelOrder(mRootView.getOrderNumber())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            mRootView.onSuccess();
                        }
                    }
                });
    }

    public void deleteOrder() {
        mModel.delete(mRootView.getOrderNumber())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            mRootView.onSuccess();
                        }
                    }
                });
    }

    public void confirmOrder() {
        mModel.confirmOrder(mRootView.getOrderNumber())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            mRootView.onSuccess();
                        }
                    }
                });
    }

    public void gbPay() {
        mModel.gbPayOrder(mRootView.getOrderNumber())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.onSuccess();
                        }
                    }
                });
    }

    public void getWxOrder() {
        mModel.wxPayOrder(mRootView.getOrderNumber())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<WXPayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<WXPayBean> s) {
                        if (s.isSuccess()) {
                            new PayUtils()
                                    .pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WX, s.getData(), "");
                            PayListenerUtils.getInstance()
                                    .setListener(mPayListener);
                        }
                    }
                });
    }

    public void getAliOrder() {
        mModel.aliPayOrder(mRootView.getOrderNumber())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<String>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<String> s) {
                        if (s.isSuccess()) {
                            new PayUtils()
                                    .pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALI, null, s.getData());
                            PayListenerUtils.getInstance()
                                    .setListener(mPayListener);
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
