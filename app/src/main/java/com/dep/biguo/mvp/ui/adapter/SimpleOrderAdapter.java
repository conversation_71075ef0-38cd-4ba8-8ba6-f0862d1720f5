package com.dep.biguo.mvp.ui.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.utils.image.ImageLoader;

import java.util.List;

public class SimpleOrderAdapter extends BaseQuickAdapter<OrderBean, BaseViewHolder> {

    public SimpleOrderAdapter(@Nullable List<OrderBean> data) {
        super(R.layout.item_shop_order_simple, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, OrderBean item) {
        // 设置分类名称
        helper.setText(R.id.tvCategory, getItemCategory(item));
        
        // 设置状态文本和控制视图显示
        setupOrderStatusAndViews(helper, item);

        // 设置商品标题和价格
        if (item.getGoods_data() != null && !item.getGoods_data().isEmpty()) {
            ShopBean firstGood = item.getGoods_data().get(0);
            helper.setText(R.id.tvTitle, firstGood.getName());

            // 设置价格
            String priceText = "¥" + firstGood.getPrice();
            helper.setText(R.id.tvPrice, priceText);

            // 加载商品图片
            ImageView ivCover = helper.getView(R.id.ivCover);
            if (!TextUtils.isEmpty(firstGood.getImg())) {
                ImageLoader.loadImage(ivCover, firstGood.getImg());
            } else {
                ivCover.setImageResource(R.drawable.order_book_image);
            }
        }

        // 设置实付金额
        helper.setText(R.id.tvPaid, "实付 ¥" + item.getTotal_fee());
    }

    /**
     * 设置订单状态和控制相关视图的显示
     */
    private void setupOrderStatusAndViews(BaseViewHolder helper, OrderBean item) {
        TextView tvStatus = helper.getView(R.id.tvStatus);
        TextView tvTip = helper.getView(R.id.tvTip);
        TextView btnAfterSale = helper.getView(R.id.btnAfterSale);

        // 根据订单状态设置显示内容
        if (item.getState() == 5) {
            // 售后中：显示"售后中"，隐藏tvTip
            tvStatus.setText("售后中");
            tvTip.setVisibility(View.GONE);
            btnAfterSale.setVisibility(View.VISIBLE);
        } else if (item.getState() == 2) {
            // 待发货：显示"待发货"，显示tvTip，隐藏btnAfterSale
            tvStatus.setText("待发货");
            tvTip.setVisibility(View.VISIBLE);
            btnAfterSale.setVisibility(View.GONE);
        } else if (item.getState() == 4) {
            // 订单已完成：显示"已完成"，隐藏tvTip，隐藏btnAfterSale
            tvStatus.setText("已完成");
            tvTip.setVisibility(View.GONE);
            btnAfterSale.setVisibility(View.GONE);
        } else {
            // 其他状态：使用原有逻辑
            tvStatus.setText(getOrderStatusText(item));

            // 默认逻辑：待付款显示提示
            if (item.getState() == 1) {
                tvTip.setVisibility(View.VISIBLE);
            } else {
                tvTip.setVisibility(View.GONE);
            }

            // 默认显示售后按钮
            btnAfterSale.setVisibility(View.VISIBLE);
        }

        // 其他按钮的显示逻辑保持不变
        helper.setGone(R.id.btnConfirm, item.getState() == 3); // 待收货状态显示确认收货按钮
    }

    /**
     * 获取订单分类名称
     */
    private String getItemCategory(OrderBean item) {
        if (TextUtils.isEmpty(item.getType())) {
            return "学习工具";
        }
        
        switch (item.getType()) {
            case "book":
                return "图书教材";
            case "video":
                return "视频课程";
            case "vip":
                return "VIP题库";
            default:
                return "学习工具";
        }
    }
    
    /**
     * 获取订单状态文字
     */
    private String getOrderStatusText(OrderBean item) {
        if (item.getRefund_status() > 0) {
            switch (item.getRefund_status()) {
                case 1:
                    return "退款申请中";
                case 2:
                    return "退款已驳回";
                case 3:
                    return "已退款";
                default:
                    return "售后中";
            }
        }

        switch (item.getState()) {
            case 1:
                return "待付款";
            case 2:
                return "待发货";
            case 3:
                return "待收货";
            case 4:
                return "待评价";
            case 5:
                return "售后中";
            case -1:
                return "已关闭";
            case -2:
                return "已取消";
            default:
                return "未知状态";
        }
    }
}