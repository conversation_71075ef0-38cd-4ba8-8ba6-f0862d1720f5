package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.TestPaperListBean;
import com.dep.biguo.mvp.contract.TestPaperListContract;
import com.dep.biguo.mvp.model.api.Api;
import com.dep.biguo.mvp.ui.adapter.TestPaperListAdapter;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.database.util.RealQuery;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class TestPaperListPresenter extends BasePresenter<TestPaperListContract.Model, TestPaperListContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject TestPaperListAdapter testPaperListAdapter;


    @Inject
    public TestPaperListPresenter(TestPaperListContract.Model model, TestPaperListContract.View rootView) {
        super(model, rootView);
    }

    public void getTestPaperList(String code) {
        ErrorHandleSubscriber<BaseResponse<List<TestPaperListBean>>> subscriber = new ErrorHandleSubscriber<BaseResponse<List<TestPaperListBean>>>(mErrorHandler) {
            @Override
            public void onNext(BaseResponse<List<TestPaperListBean>> response) {
                if (response.isSuccess()) {
                    if(AppUtil.isEmpty(response.getData())){
                        mRootView.showEmptyView();
                        mRootView.getTestPaperListSuccess(new ArrayList<>());
                    }else {
                        mRootView.showSuccessView();
                        mRootView.getTestPaperListSuccess(response.getData());

                        RealQuery.insertSimu(code, response.getData());
                    }
                }else if(AppUtil.isEmpty(testPaperListAdapter.getData())) {
                    mRootView.showErrorView(null);
                }
            }

            @Override
            public void onError(Throwable t) {
                super.onError(t);
                if(AppUtil.isEmpty(testPaperListAdapter.getData())) {
                    mRootView.showErrorView(t);
                }
            }
        };

        //先用缓存显示页面，待请求完成，再用服务器的填充一次，并更新缓存
        RealQuery.querySumi(code, list -> {
            BaseResponse<List<TestPaperListBean>> response = new BaseResponse<>();
            response.setResult_code(Api.REQUEST_SUCCESS);
            response.setResult_info("请求成功");
            response.setData(list);
            subscriber.onNext(response);
        });

        mModel.getTestPaperList(code)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(subscriber);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
