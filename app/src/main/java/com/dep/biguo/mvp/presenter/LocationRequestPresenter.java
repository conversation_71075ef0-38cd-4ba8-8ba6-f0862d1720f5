package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CityBean;
import com.dep.biguo.mvp.contract.LocationRequestContract;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class LocationRequestPresenter extends BasePresenter<LocationRequestContract.Model, LocationRequestContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private List<CityBean> cityBeanList;

    @Inject
    public LocationRequestPresenter(LocationRequestContract.Model model, LocationRequestContract.View rootView) {
        super(model, rootView);
    }
    public void getCity() {
        mModel.getCity()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<CityBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<CityBean>> response) {
                        if (response.isSuccess()) {
                            cityBeanList = response.getData();
                            mRootView.setCitySuccess(response.getData());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }



    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
