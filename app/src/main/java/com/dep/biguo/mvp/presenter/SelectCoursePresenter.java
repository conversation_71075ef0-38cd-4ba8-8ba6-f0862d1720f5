package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CourseBean;
import com.dep.biguo.bean.SelectCourseBean;
import com.dep.biguo.mvp.contract.SelectCourseContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.database.util.RealQuery;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class SelectCoursePresenter extends BasePresenter<SelectCourseContract.Model, SelectCourseContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private CourseBean courseBean;

    @Inject
    public SelectCoursePresenter(SelectCourseContract.Model model, SelectCourseContract.View rootView) {
        super(model, rootView);
    }

    public void getCourse(){
        //请求服务器
        mModel.getCourse(UserCache.getProfession().getId(), UserCache.getSchool().getId(), true)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<CourseBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<CourseBean> response) {
                        if (response.isSuccess()) {
                            mRootView.showSuccessView();
                            courseBean = response.getData();
                            //后台的课程数据
                            mRootView.getCourseSuccess(response.getData());

                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    /**管理报考
     *
     */
    public void manageCourse(List<CourseBean.CourseItemBean> formList, CourseBean.CourseItemBean join, CourseBean.CourseItemBean againJoin, CourseBean.CourseItemBean cancel) {
        String joinCourseId = join == null ? "" : (join.getCourses_id()+"");
        String againJoinCourseId = againJoin == null ? "" : (againJoin.getCourses_id()+"");
        String cancelCourseId = cancel == null ? "" : (cancel.getCourses_id()+"");

        if(UserCache.getUserCache() == null) return;

        mModel.manageCourse(joinCourseId, againJoinCourseId, cancelCourseId)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        //同步到服务器不需要等服务器响应
                        if(response.isSuccess()) {

                            if(join != null) {
                                formList.remove(join);
                                courseBean.getCourses_joined().add(join);
                                mRootView.manageCourseSuccess();
                            }else if(againJoin != null){
                                formList.remove(againJoin);
                                courseBean.getCourses_joined().add(againJoin);
                                againJoin.setScore(0);
                                mRootView.manageCourseSuccess();
                            }else {
                                formList.remove(cancel);
                                courseBean.getCourses_not_joined().add(cancel);
                                mRootView.manageCourseSuccess();
                            }
                        }
                    }
                });
    }


    public void setScore(CourseBean.CourseItemBean courseItemBean, String score){
        if(TextUtils.isEmpty(score)){
            mRootView.showMessage("请输入成绩");
            return;
        }

        mModel.setScore(courseItemBean.getCourses_id(), score)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse baseResponse) {
                        if(baseResponse.isSuccess()){
                            try {
                                courseItemBean.setScore(Float.parseFloat(score));
                            }catch (NumberFormatException e){
                                mRootView.showMessage("成绩格式错误");
                            }

                            if(courseItemBean.getScore() >= courseBean.getPass_score()) {
                                if(courseBean.getCourses_joined().contains(courseItemBean)) {
                                    courseBean.getCourses_joined().remove(courseItemBean);
                                }else if(courseBean.getCourses_not_joined().contains(courseItemBean)){
                                    courseBean.getCourses_not_joined().remove(courseItemBean);
                                }
                                courseBean.getCourses_passed().add(courseItemBean);
                            }
                            mRootView.manageCourseSuccess();
                        }
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
