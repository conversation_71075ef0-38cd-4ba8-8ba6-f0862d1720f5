package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.mvp.contract.CourseFeedbackContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

@ActivityScope
public class CourseFeedbackPresenter extends BasePresenter<CourseFeedbackContract.Model, CourseFeedbackContract.View> {

    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    @Inject
    public CourseFeedbackPresenter(CourseFeedbackContract.Model model, CourseFeedbackContract.View rootView) {
        super(model, rootView);
    }

    public void courseFeedback() {
        Map<String, Object> params = new HashMap<>();
        if (mRootView.getMainType() == 0) {
            mRootView.showMessage("选择一个题库类型");
            return;
        }
        if (TextUtils.isEmpty(mRootView.getMessage())) {
            mRootView.showMessage("选择一个反馈类型");
            return;
        }

        params.put("type", mRootView.getMainType());
        params.put("feedback_type", 1);
        params.put("quiz_id", mRootView.getCode());
        params.put("msg", mRootView.getMessage());

        if (!TextUtils.isEmpty(mRootView.getContent()))
            params.put("extra_msg", mRootView.getContent());

        mModel.courseFeedback(params)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.showMessage(response.getResult_info());
                            mRootView.killMyself();
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}