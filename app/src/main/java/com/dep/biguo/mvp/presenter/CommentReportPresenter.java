package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.content.Intent;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CommentReportBean;
import com.dep.biguo.mvp.contract.CommentReportContract;
import com.dep.biguo.mvp.ui.activity.CommentReportActivity;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class CommentReportPresenter extends BasePresenter<CommentReportContract.Model, CommentReportContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    private int mParse_id;
    private String mCode;

    @Inject
    public CommentReportPresenter(CommentReportContract.Model model, CommentReportContract.View rootView) {
        super(model, rootView);
    }

    public void init(Intent intent){
        mParse_id = intent.getIntExtra(CommentReportActivity.PARSE_ID, 0);
        mCode = intent.getStringExtra(CommentReportActivity.CODE);
    }

    /**获取举报类型
     *
     */
    public void getReportType(){
        mModel.getReportType()
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<CommentReportBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<CommentReportBean>> s) {
                        if (s.isSuccess()) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.showEmptyView();
                                mRootView.getReportTypeSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getReportTypeSuccess(s.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    /**提交举报
     * @param type 举报的类型
     * @param content 举报内容
     */
    public void report(String type, String content){
        mModel.report(mParse_id, mCode, type, content)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                        }
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
