package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.SkillHomeClassBean;
import com.dep.biguo.mvp.contract.SkillHomeClassContract;
import com.dep.biguo.mvp.ui.adapter.SkillHomeAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class SkillHomeClassPresenter extends BasePresenter<SkillHomeClassContract.Model, SkillHomeClassContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    SkillHomeAdapter skillHomeAdapter;

    private int mPage = 1;
    private static final int INIT = 0;
    private static final int REFRESH = 1;
    private static final int LOAD = 2;

    @Inject
    public SkillHomeClassPresenter(SkillHomeClassContract.Model model, SkillHomeClassContract.View rootView) {
        super(model, rootView);
    }

    public void refresh(int skill_id, int source_type, String className){
        getData(skill_id, source_type, className, REFRESH,1);
    }

    public void loadMore(int skill_id, int source_type, String className){
        getData(skill_id, source_type, className, LOAD, mPage+1);
    }

    private void getData(int skill_id, int source_type, String className, int action, int page) {
        mModel.getData(skill_id, source_type, className, page)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if (action == INIT)
                        mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> {
                    if (action == INIT)
                        mRootView.hideLoading();
                })
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<SkillHomeClassBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<SkillHomeClassBean> s) {
                        if (s.isSuccess()) {
                            if (action == INIT || action == REFRESH) {
                                if (AppUtil.isEmpty(s.getData().getList())) {
                                    mRootView.showEmptyView();
                                }else {
                                    mRootView.finishRefresh();
                                }
                                skillHomeAdapter.setNewData(s.getData().getList());
                                mPage = 1;//刷新成功，将页数设置为1
                            } else {
                                skillHomeAdapter.addData(s.getData().getList());
                                skillHomeAdapter.loadMoreComplete();
                                mPage += 1;//加载更多成功，将请求页数自加1
                            }

                            if (AppUtil.isEmpty(s.getData().getList()))
                                skillHomeAdapter.loadMoreEnd();
                        }else {
                            refreshOrLoadAnimation();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        refreshOrLoadAnimation();
                        if (AppUtil.isEmpty(skillHomeAdapter.getData()))
                            mRootView.showErrorView(t);
                    }

                    //取消刷新或加载的动画
                    public void refreshOrLoadAnimation(){
                        switch (action){
                            case REFRESH:mRootView.finishRefresh();break;
                            case LOAD:skillHomeAdapter.loadMoreFail();break;
                        }
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
