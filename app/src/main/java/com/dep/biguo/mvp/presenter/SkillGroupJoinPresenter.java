package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.os.Bundle;
import android.os.CountDownTimer;

import androidx.annotation.NonNull;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.GroupJoinBean;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.SkillGroupJoinContract;
import com.dep.biguo.mvp.ui.adapter.GroupJoinAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class SkillGroupJoinPresenter extends BasePresenter<SkillGroupJoinContract.Model, SkillGroupJoinContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    GroupJoinAdapter mGroupJoinAdapter;

    private int mPage = 1;
    private static final int INIT = 0;
    private static final int REFRESH = 1;
    private static final int LOAD = 2;

    private int limit = 20;//每一页请求数据的条数
    private String goodsType;
    private int sendGroupId;//发起拼团后，返回的订单ID，也是这个团的ID
    private int skill_id;//技能证ID

    private List<DiscountBean> discountList;//优惠券列表
    private PayResultListener mResultPayListener;//支付的监听接口

    private CountDownTimer mCountDownTimer;

    public void initRefresh(){
        discountList = null;
        getData(INIT,1);
    }

    public void refresh(){
        getData(REFRESH,1);
    }

    public void loadMore(){
        getData(LOAD,mPage+1);
    }

    @Inject
    public SkillGroupJoinPresenter(SkillGroupJoinContract.Model model, SkillGroupJoinContract.View rootView) {
        super(model, rootView);
    }


    public void init(Bundle bundle){
        goodsType = bundle != null ? bundle.getString(StartFinal.GOODS_TYPE) : "";
        skill_id = bundle != null ? bundle.getInt(StartFinal.SKILL_ID) : 0;
        initRefresh();

        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess();
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {
                sendGroupId = 0;
                mRootView.payCancel();
            }
        };

        //初始化倒计时
        mCountDownTimer = new CountDownTimer(86400 * 1000, 100) {
            @Override
            public void onTick(long millisUntilFinished) {
                computerCountDown();
            }

            @Override
            public void onFinish() {

            }
        };
    }

    public String getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }

    public int getSendGroupId() {
        return sendGroupId;
    }

    public void getData(int action, int page) {
        //0表示旧版的接口，不返回发起拼团的数据，1表示新版本的接口，需要返回发起拼团的数据
        mModel.getData(skill_id, goodsType, page, limit)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> { if(action == INIT) mRootView.showLoading(); })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<GroupJoinBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<GroupJoinBean> s) {
                        if (s.isSuccess()) {
                            List<GroupJoinBean.GroupBean> groupBeanList = new ArrayList<>(s.getData().getJoin_list());
                            groupBeanList.addAll(s.getData().getOpen_list());

                            mCountDownTimer.cancel();//先结束倒计时，再重新开始倒计时，避免出现多个倒计时
                            if (action == INIT || action == REFRESH) {
                                mGroupJoinAdapter.setNewData(groupBeanList);
                                mRootView.finishRefresh();
                                if (AppUtil.isEmpty(groupBeanList)) {
                                    mRootView.showEmptyView();
                                }else {
                                    mRootView.hideLoading();
                                }
                                mPage = 1;//刷新成功，将页数设置为1
                            } else {
                                mGroupJoinAdapter.addData(groupBeanList);
                                mGroupJoinAdapter.loadMoreComplete();
                                mPage += 1;//加载更多成功，将请求页数自加1
                            }

                            if(mGroupJoinAdapter.getItemCount() > 0) {
                                mCountDownTimer.start();//开始倒计时
                            }

                            if(!AppUtil.isEmpty(groupBeanList)) {
                                for (int i = 0; i <groupBeanList.size(); i++) {
                                    GroupJoinBean.GroupBean groupBean = groupBeanList.get(i);
                                    //去参团才有倒计时
                                    if(groupBean.getSec() > 0) {
                                        groupBean.setSec(groupBean.getSec() * 1000);
                                    }
                                }
                            }
                            if(s.getData().getJoin_list().size() < limit){
                                mGroupJoinAdapter.loadMoreEnd();
                            }
                        }else {
                            refreshOrLoadAnimation();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        refreshOrLoadAnimation();
                        if (AppUtil.isEmpty(mGroupJoinAdapter.getData()))
                            mRootView.showErrorView(t);
                    }

                    //取消刷新或加载的动画
                    public void refreshOrLoadAnimation(){
                        switch (action){
                            case REFRESH:mRootView.finishRefresh();break;
                            case LOAD:mGroupJoinAdapter.loadMoreFail();break;
                        }
                    }
                });
    }

    /**获取优惠券
     *
     */
    public void getDiscountCard(boolean isGroup, int position, int newcomers){
        if(discountList != null) {
            mRootView.showPayDialog(isGroup, discountList, position, newcomers);
            return;
        }

        mModel.getDiscountCard(1, mGroupJoinAdapter.getItem(position).getType(), "use", -1)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<DiscountBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<List<DiscountBean>> s) {
                        if (s.isSuccess()) {
                            discountList = s.getData();
                            mRootView.showPayDialog(isGroup, discountList, position, newcomers);
                        }
                    }
                    @Override
                    public void onError(@NonNull Throwable t) {
                        mRootView.showMessage("获取优惠券列表失败");
                        mRootView.showPayDialog(isGroup, new ArrayList<>(), position, newcomers);
                    }
                });
    }

    public void pay(boolean isGroup, GroupJoinBean.GroupBean groupBean, String payType, String groupId, DiscountBean selectedDiscountBean) {
        //创建需要传递的参数对象
        Map<String,Object> paramsMap = getParams(groupBean, payType, groupId, selectedDiscountBean).getParamsMap();
        //根据是否拼团选择对应的接口
        Observable<BaseResponse<PayBean>> observable = isGroup ? mModel.payGroupOrder(paramsMap) : mModel.paySingleOrder(paramsMap);

        observable.subscribeOn(Schedulers.io())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            sendGroupId = s.getData().getOrder_id();
                            //当调起的订单使用了优惠券，优惠券此时被占用，则需要将使用的优惠券从优惠券列表中移除
                            if(selectedDiscountBean != null) {
                                discountList.remove(selectedDiscountBean);
                            }
                            //优惠额度大于等于支付价格，不需要调起支付界面，处理方式与果币、积分支付一致，
                            if(s.getData() != null && s.getData().getPay() instanceof Boolean){
                                mResultPayListener.onPaySuccess();
                                return;
                            }

                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                WXPayBean wxPayBean = GsonUtils.fromJson(GsonUtils.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_COIN.equals(payType) || PayUtils.PAY_TYPE_INTEGRAL.equals(payType)){
                                mResultPayListener.onPaySuccess();
                            }
                        }
                    }
                });
    }

    /**创建一个创建订单需要的参数集合
     * @param payType 支付方式{@link PayUtils#PAY_TYPE_WEXIN,PayUtils#PAY_TYPE_ALIPAY,PayUtils#PAY_TYPE_INTEGRAL,PayUtils#PAY_TYPE_COIN}
     * @return
     */
    private PayParamsBean getParams(GroupJoinBean.GroupBean groupBean, String payType, String groupId, DiscountBean selectedDiscountBean) {
        PayParamsBean paramsBean = PayParamsBean.init();
        paramsBean.put(PayParamsBean.TYPE, groupBean.getType());
        paramsBean.put(PayParamsBean.GROUP_ID, groupId);
        paramsBean.put(PayParamsBean.PAY_TYPE, payType);
        paramsBean.put(PayParamsBean.COUPON_ID, selectedDiscountBean == null ? 0 : selectedDiscountBean.getId());
        paramsBean.put(PayParamsBean.PRODUCT_ID, groupBean.getProduct_id());
        paramsBean.put(PayParamsBean.SKILL_ID, groupBean.getSkill_id());
        paramsBean.put(PayParamsBean.SOURCE_TYPE, groupBean.getSource_type());

        return paramsBean;
    }

    public String getGoodTypeName(String goodsType){
        return StartFinal.SKILL_VIDEO.equals(goodsType) ? "职业技能" : "职场提升";
    }

    /**计算倒计时
     *
     */
    private void computerCountDown() {
        if (AppUtil.isEmpty(mGroupJoinAdapter.getData())) return;

        for (GroupJoinBean.GroupBean bean : mGroupJoinAdapter.getData()) {
            bean.setSec(bean.getSec() <= 0 ? 0 : (bean.getSec() - 100));
        }

        mGroupJoinAdapter.notifyTime();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        if(mCountDownTimer != null) mCountDownTimer.cancel();
        LoadingDialog.hideLoadingDialog();
    }
}
