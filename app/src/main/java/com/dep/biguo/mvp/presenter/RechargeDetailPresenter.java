package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.RechargeDetailBean;
import com.dep.biguo.mvp.contract.RechargeDetailContract;
import com.dep.biguo.mvp.ui.adapter.RechargeDetailAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.StartFinal;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class RechargeDetailPresenter extends BasePresenter<RechargeDetailContract.Model, RechargeDetailContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject RechargeDetailAdapter mAdapter;

    private int type;
    private int position;

    @Inject
    public RechargeDetailPresenter(RechargeDetailContract.Model model, RechargeDetailContract.View rootView) {
        super(model, rootView);
    }

    public void init(int type, int position){
        this.type = type;
        this.position = position;
    }
    /**获取签到数据
     */
    public void get_integral_detail(int page) {
        Observable<BaseResponse<List<RechargeDetailBean>>> observable;
        if(type == StartFinal.RECHARGE){//果币
            observable = mModel.get_guobi_detail(page, position);
        }else {//积分
            observable = mModel.get_integral_detail(page, position);
        }

        observable.subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() ->  mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<RechargeDetailBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<RechargeDetailBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showEmptyView();
                                mRootView.loadSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.loadSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
