package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.mvp.contract.JSZMainActivityContract;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.jiguangPush.JPushHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.umengPush.UmengPushHelper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.simple.eventbus.EventBus;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;


/**
 * ================================================
 * Description:
 * <p>
 * Created by MVPArmsTemplate on 05/11/2020 15:41
 * <a href="mailto:<EMAIL>">Contact me</a>
 * <a href="https://github.com/JessYanCoding">Follow me</a>
 * <a href="https://github.com/JessYanCoding/MVPArms">Star me</a>
 * <a href="https://github.com/JessYanCoding/MVPArms/wiki">See me</a>
 * <a href="https://github.com/JessYanCoding/MVPArmsTemplate">模版请保持更新</a>
 * ================================================
 */
@ActivityScope
public class JSZMainActivityPresenter extends BasePresenter<JSZMainActivityContract.Model, JSZMainActivityContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    @Inject
    public JSZMainActivityPresenter(JSZMainActivityContract.Model model, JSZMainActivityContract.View rootView) {
        super(model, rootView);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }

    public void getUserInfo() {
        if (UserCache.getUserCache() == null) return;

        mModel.getUserInfo()
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<UserBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<UserBean> response) {
                        if (response.isSuccess()) {
                            UserCache.cacheUser(response.getData());
                            EventBus.getDefault().post(UserCache.getUserCache(), EventBusTags.GET_USERINFO_SUCCESS);

                        }
                    }
                });
    }

    public void bindPushClientId() {
        UmengPushHelper.addExamInfoTag(mRootView.getActivity()); //注册友盟推送标签
        UmengPushHelper.addGradeTag(mRootView.getActivity(), "skill"); //注册友盟推送标签
        if (UserCache.getUserCache() == null) return;

        String device_token = AppUtil.isEmpty(UmengPushHelper.getDeviceToken(mRootView.getActivity()), "");
        String registerId = AppUtil.isEmpty(JPushHelper.getRegistrationID(mRootView.getActivity()), "");

        if(TextUtils.isEmpty(device_token) && TextUtils.isEmpty(registerId)) return;

        //2表示是友盟,3表示友盟和极光都传
        mModel.bindPushClientId(3, device_token, registerId)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        //解析数据
                        Gson gson = new Gson();
                        List<String> tags = gson.fromJson(gson.toJson(response.getData()), new TypeToken<List<String>>(){}.getType());
                        UmengPushHelper.setLoginAlias(mRootView.getActivity());//注册友盟别名
                        UmengPushHelper.setLoginTag(mRootView.getActivity(), tags); //注册友盟推送标签
                    }
                });
    }

    public void addPushTag(String...tags) {
        if (UserCache.getUserCache() == null || tags.length == 0) return;

        mModel.addPushTag(tags)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        LogUtil.d("dddd","add tag to service is "+response.isSuccess());
                    }
                });
    }
}
