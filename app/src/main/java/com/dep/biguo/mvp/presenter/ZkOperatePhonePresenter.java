package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.os.CountDownTimer;
import android.text.TextUtils;

import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.bean.VerifyCodeBean;
import com.dep.biguo.mvp.contract.ZkOperatePhoneContract;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.utils.MatcherUtil;
import com.dep.biguo.utils.RSAEncrypt;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.simple.eventbus.EventBus;

import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class ZkOperatePhonePresenter extends BasePresenter<ZkOperatePhoneContract.Model, ZkOperatePhoneContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private CountDownTimer mCountDownTimer;

    @Inject
    public ZkOperatePhonePresenter(ZkOperatePhoneContract.Model model, ZkOperatePhoneContract.View rootView) {
        super(model, rootView);

        mCountDownTimer = new CountDownTimer(60 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                mRootView.setVerifyTimerText(String.format("%s秒", (int) millisUntilFinished / 1000), false);
            }

            @Override
            public void onFinish() {
                mRootView.setVerifyTimerText("重新发送", true);
            }
        };
    }

    /**获取短信验证码
     * @param phone 手机号
     * @param captcha 图形验证码
     */
    public void getSmsVerifyCode(String phone, String captcha, int type) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("mobile", phone);
            params.put("captcha", captcha);
            //type取值有4、5、6、7、8
            params.put("type", type);//1验证码登录，2手机号修改密码，3重置密码，4微信登录绑定手机号，5更换手机号验证账号，6更换手机号-更新手机号，7更换支付宝账号验证账号，8注销账号
            mModel.getSmsVerifyCode(RSAEncrypt.encryptByPublicKey(GsonUtils.toJson(params)))
                    .subscribeOn(Schedulers.io())
                    .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                    .subscribeOn(AndroidSchedulers.mainThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .doFinally(() -> mRootView.hideLoadingDialog())
                    .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                    .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                        @Override
                        public void onNext(BaseResponse userBeanBaseResponse) {
                            if (userBeanBaseResponse.isSuccess()) {
                                mCountDownTimer.start();
                                mRootView.showMessage(userBeanBaseResponse.getResult_info());
                            }
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**获取图形验证码
     * @param phone 手机号
     */
    public void getImageVerifyCode(String phone) {
        try {
            mModel.getImageVerifyCode(phone)
                    .subscribeOn(Schedulers.io())
                    .subscribeOn(AndroidSchedulers.mainThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                    .subscribe(new ErrorHandleSubscriber<BaseResponse<VerifyCodeBean>>(mErrorHandler) {
                        @Override
                        public void onNext(BaseResponse<VerifyCodeBean> userBeanBaseResponse) {
                            if (userBeanBaseResponse.isSuccess()) {
                                if(!TextUtils.isEmpty(userBeanBaseResponse.getData().getImg_url())){
                                    mRootView.getImageVerifyCodeSuccess(userBeanBaseResponse.getData().getImg_url());
                                    return;
                                }
                            }
                            mRootView.showMessage("获取图片验证码失败");
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**微信登录绑定手机号
     * @param phone 手机号
     * @param verifyCode 验证码
     * @param unionid 微信返回的unionid
     */
    public void wechatBindPhone(String phone, String verifyCode, String unionid) {
        if (TextUtils.isEmpty(verifyCode)) {
            mRootView.showMessage("请输入短信验证码");
            return;
        }

        String address = AppUtil.isEmpty(UserCache.getAddress(), "");
        String longitude = AppUtil.isEmpty(UserCache.getLongitude(), "");
        String latitude = AppUtil.isEmpty(UserCache.getLatitude(), "");

        mModel.wechatBindPhone(address, longitude, latitude,2, 2, phone, verifyCode, unionid)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<UserBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<UserBean> response) {
                        if (response.isSuccess()) {
                            EventBus.getDefault().post(response.getData(), EventBusTags.LOGIN_WECHAT_BIND);
                            mRootView.killMyself();
                        }
                    }
                });
    }

    /**微信登录绑定手机号
     * @param phone 手机号
     * @param verifyCode 验证码
     * @param openid QQ登录返回的oenpid
     */
    public void qqBindPhone(String phone, String verifyCode, String openid) {
        if (TextUtils.isEmpty(verifyCode)) {
            mRootView.showMessage("请输入短信验证码");
            return;
        }

        String address = AppUtil.isEmpty(UserCache.getAddress(), "");
        String longitude = AppUtil.isEmpty(UserCache.getLongitude(), "");
        String latitude = AppUtil.isEmpty(UserCache.getLatitude(), "");

        mModel.qqBindPhone(address, longitude, latitude,2, 2, phone, verifyCode, openid)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<UserBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<UserBean> response) {
                        if (response.isSuccess()) {
                            EventBus.getDefault().post(response.getData(), EventBusTags.LOGIN_QQ_BIND);
                            mRootView.killMyself();
                        }
                    }
                });
    }

    /**验证图形验证码是否正确
     * @param old_mobile  当前与账号绑定的手机号
     * @param verifyCode 短信验证码
     */
    public void isSmsCorrect(String old_mobile, String verifyCode, int type) {
        if (TextUtils.isEmpty(verifyCode)) {
            mRootView.showMessage("验证码不能为空");
            return;
        }
        mModel.isSmsCorrect(old_mobile, verifyCode, type)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mCountDownTimer.cancel();
                            mRootView.setVerifyTimerText("获取验证码", true);
                            mRootView.smsCorrectSuccess();
                        } else {
                            mRootView.showMessage(response.getResult_info());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }

    /**修改手机号
     **/
    public void updateMobile(String new_mobile, String verifyCode, String old_mobile) {
        if (!MatcherUtil.matchMobile(new_mobile)) {
            mRootView.showMessage("新手机号格式不正确");
            return;
        }
        if (new_mobile.equals(old_mobile)) {
            mRootView.showMessage("手机号无变化");
            return;
        }
        if (TextUtils.isEmpty(verifyCode)) {
            mRootView.showMessage("验证码不能为空");
            return;
        }
        mModel.updateMobile(new_mobile, verifyCode, old_mobile)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.showMessage(response.getResult_info());
                            UserBean userBean = UserCache.getUserCache();
                            userBean.setMobile(new_mobile);
                            UserCache.cacheUser(userBean);
                            mRootView.killMyself();
                        } else {
                            mRootView.hideLoadingDialog();
                            mRootView.showMessage(response.getResult_info());
                        }
                    }
                });
    }

    /**注销账号
     **/
    public void writeOffUser(String mobile, String verifycode) {
        mModel.writeOffUser(mobile, verifycode)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            UserCache.removeUserCache();
                            mRootView.showMessage(response.getResult_info());
                            EventBus.getDefault().post(new UserBean(), EventBusTags.LOGOUT_SUCCESS);
                            mRootView.killMyself();
                        } else {
                            mRootView.showMessage(response.getResult_info());
                        }
                    }
                });
    }

    /**注销账号
     **/
    public void writeOffUserHint() {
        mModel.writeOffUserHint()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.writeOffUserHintSuccess(response.getData().toString());
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        if(mCountDownTimer != null){
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }
        LoadingDialog.hideLoadingDialog();
    }
}
