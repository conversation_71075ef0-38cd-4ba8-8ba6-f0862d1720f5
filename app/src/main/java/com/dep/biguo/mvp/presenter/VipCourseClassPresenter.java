package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.VipCourseClassBean;
import com.dep.biguo.mvp.contract.VipCourseClassContract;
import com.dep.biguo.mvp.ui.adapter.VipCourseClassAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.ActivityEvent;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;


/**
 * ================================================
 * Description:
 * <p>
 * Created by MVPArmsTemplate on 06/23/2020 15:33
 * <a href="mailto:<EMAIL>">Contact me</a>
 * <a href="https://github.com/JessYanCoding">Follow me</a>
 * <a href="https://github.com/JessYanCoding/MVPArms">Star me</a>
 * <a href="https://github.com/JessYanCoding/MVPArms/wiki">See me</a>
 * <a href="https://github.com/JessYanCoding/MVPArmsTemplate">模版请保持更新</a>
 * ================================================
 */
@ActivityScope
public class VipCourseClassPresenter extends BasePresenter<VipCourseClassContract.Model, VipCourseClassContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;
    @Inject
    VipCourseClassAdapter mAdapter;

    @Inject
    public VipCourseClassPresenter(VipCourseClassContract.Model model, VipCourseClassContract.View rootView) {
        super(model, rootView);
    }

    public void getVipcourseClass(int classroom_id, boolean showLoading) {
        mModel.getVipcourseClass(classroom_id)
                .subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> {
                    if (showLoading)
                        mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> {
                    if (showLoading)
                        mRootView.hideLoading();
                })
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<VipCourseClassBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<VipCourseClassBean>> s) {
                        if (s.isSuccess()) {
                            if (AppUtil.isEmpty(s.getData())) {
                                mRootView.showEmptyView();
                            } else {
                                mAdapter.setNewData(s.getData());
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        this.mAdapter = null;
    }
}
