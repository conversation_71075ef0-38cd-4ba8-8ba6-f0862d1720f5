package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.InvitorBean;
import com.dep.biguo.mvp.contract.HasInvitePeopleContract;
import com.dep.biguo.mvp.ui.adapter.InvitorAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.FragmentEvent;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class HasInvitePeoplePresenter extends BasePresenter<HasInvitePeopleContract.Model, HasInvitePeopleContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public HasInvitePeoplePresenter(HasInvitePeopleContract.Model model, HasInvitePeopleContract.View rootView) {
        super(model, rootView);
    }


    public void getInviterListData(int page) {
        mModel.inviterList(page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.DESTROY_VIEW))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<InvitorBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<InvitorBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showEmptyView();
                                mRootView.inviterListSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.inviterListSuccess(response.getData());
                            }

                        } else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }
}
