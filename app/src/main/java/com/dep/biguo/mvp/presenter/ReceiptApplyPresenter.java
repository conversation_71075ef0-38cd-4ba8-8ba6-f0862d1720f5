package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.ReceiptTitleBean;
import com.dep.biguo.mvp.contract.ReceiptApplyContract;
import com.biguo.utils.widget.LoadingDialog;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.plv.thirdpart.blankj.utilcode.util.AppUtils;

import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class ReceiptApplyPresenter extends BasePresenter<ReceiptApplyContract.Model, ReceiptApplyContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private List<ReceiptTitleBean> titleList;

    @Inject
    public ReceiptApplyPresenter(ReceiptApplyContract.Model model, ReceiptApplyContract.View rootView) {
        super(model, rootView);
    }

    public List<ReceiptTitleBean> getTitleList() {
        return titleList;
    }

    public void getReceiptTitleList(boolean isRefresh){
        if(!AppUtil.isEmpty(titleList) && !isRefresh){
            mRootView.getReceiptTitleListSuccess(this.titleList, isRefresh);
            return;
        }
        mModel.getReceiptTitleList()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if(isRefresh) {
                        mRootView.showLoadingDialog();
                    }else {
                        mRootView.showLoadingView();
                    }
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() ->{
                    mRootView.hideLoadingDialog();
                    mRootView.showSuccessView();
                })
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<ReceiptTitleBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<ReceiptTitleBean>> response) {
                        if (response.isSuccess()) {
                            titleList = response.getData();
                            mRootView.getReceiptTitleListSuccess(response.getData(), isRefresh);
                        }
                    }
                });
    }

    public void commitReceiptApply(int titleId, String orderNumber, int is_again_apply){
        mModel.commitReceiptApply(titleId, orderNumber, is_again_apply)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.commitReceiptApplySuccess();
                        }
                    }
                });
    }

    public void delReceiptTitle(ReceiptTitleBean select){
        mModel.delReceiptTitle(select.getId())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.delReceiptTitleSuccess(select);
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
