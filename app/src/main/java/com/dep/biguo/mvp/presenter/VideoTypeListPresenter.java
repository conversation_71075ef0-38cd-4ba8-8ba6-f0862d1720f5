package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;
import android.content.Intent;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.VideoTypeListBean;
import com.dep.biguo.mvp.contract.VideoTypeListContract;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class VideoTypeListPresenter extends BasePresenter<VideoTypeListContract.Model, VideoTypeListContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;


    @Inject
    public VideoTypeListPresenter(VideoTypeListContract.Model model, VideoTypeListContract.View rootView) {
        super(model, rootView);
    }

    public void getVideoTypeList(int courseId){
        mModel.getVideoTypeList(courseId)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<VideoTypeListBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<VideoTypeListBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showEmptyView();
                                mRootView.getVideoTypeListSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getVideoTypeListSuccess(response.getData());
                            }
                        }else {
                            mRootView.showEmptyView();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
